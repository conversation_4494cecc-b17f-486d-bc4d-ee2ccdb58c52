#!/bin/bash

# Test Mixpanel API endpoints to explore available data
# This script helps us understand what events and data are available

# Mixpanel credentials from .env
PROJECT_ID="3318299"
USERNAME="phia-interior-dashboard.f2b854.mp-service-account"
SECRET="wEbByBryUFALWlAG7A5fsqbJK1SGpSUG"
WORKSPACE_ID="3822915"

# Base URL
BASE_URL="https://mixpanel.com/api/query"

# Date range (last 7 days)
END_DATE=$(date +%Y-%m-%d)
START_DATE=$(date -d '7 days ago' +%Y-%m-%d)

echo "🔍 Testing Mixpanel API with project: $PROJECT_ID"
echo "📅 Date range: $START_DATE to $END_DATE"
echo "=" * 50

# Test 1: Get a sample of user profiles to see what properties exist
echo "1️⃣ Testing user profiles endpoint (/engage)..."
echo "Getting first 10 user profiles to see available properties..."

curl -s -u "$USERNAME:$SECRET" \
  -X POST \
  "$BASE_URL/engage" \
  -d "project_id=$PROJECT_ID" \
  -d "workspace_id=$WORKSPACE_ID" \
  -d "page=0" | jq '.' > user_profiles_sample.json

if [ -s user_profiles_sample.json ]; then
    echo "✅ User profiles response saved to user_profiles_sample.json"
    echo "📊 Sample user properties:"
    jq -r '.results[0].$properties | keys[]' user_profiles_sample.json 2>/dev/null | head -10
else
    echo "❌ No user profiles data received"
fi

echo ""

# Test 2: Try to get events data (we need to know what events exist first)
echo "2️⃣ Testing events endpoint with common event names..."

# Common extension events to test
EVENTS='["heartbeat", "extension_loaded", "page_view", "click", "user_action", "feature_used"]'

curl -s -u "$USERNAME:$SECRET" \
  -G "$BASE_URL/events" \
  --data-urlencode "project_id=$PROJECT_ID" \
  --data-urlencode "workspace_id=$WORKSPACE_ID" \
  --data-urlencode "event=$EVENTS" \
  --data-urlencode "type=general" \
  --data-urlencode "unit=day" \
  --data-urlencode "from_date=$START_DATE" \
  --data-urlencode "to_date=$END_DATE" | jq '.' > events_sample.json

if [ -s events_sample.json ]; then
    echo "✅ Events response saved to events_sample.json"
    echo "📈 Available events with data:"
    jq -r '.data.values | keys[]' events_sample.json 2>/dev/null
else
    echo "❌ No events data received"
fi

echo ""

# Test 3: Try to get any available cohorts
echo "3️⃣ Testing cohorts endpoint (/cohorts/list)..."

curl -s -u "$USERNAME:$SECRET" \
  -X POST \
  "$BASE_URL/cohorts/list" \
  -d "project_id=$PROJECT_ID" \
  -d "workspace_id=$WORKSPACE_ID" | jq '.' > cohorts_sample.json

if [ -s cohorts_sample.json ]; then
    echo "✅ Cohorts response saved to cohorts_sample.json"
    echo "👥 Available cohorts:"
    jq -r '.results[].name' cohorts_sample.json 2>/dev/null
else
    echo "❌ No cohorts data received"
fi

echo ""

# Test 4: Try segmentation with a common property
echo "4️⃣ Testing segmentation endpoint..."

curl -s -u "$USERNAME:$SECRET" \
  -G "$BASE_URL/segmentation" \
  --data-urlencode "project_id=$PROJECT_ID" \
  --data-urlencode "workspace_id=$WORKSPACE_ID" \
  --data-urlencode "event=page_view" \
  --data-urlencode "from_date=$START_DATE" \
  --data-urlencode "to_date=$END_DATE" \
  --data-urlencode "on=properties.\$browser" \
  --data-urlencode "type=unique" \
  --data-urlencode "unit=day" \
  --data-urlencode "limit=10" | jq '.' > segmentation_sample.json

if [ -s segmentation_sample.json ]; then
    echo "✅ Segmentation response saved to segmentation_sample.json"
    echo "🌐 Top segments:"
    jq -r '.data.values | keys[]' segmentation_sample.json 2>/dev/null | head -5
else
    echo "❌ No segmentation data received"
fi

echo ""
echo "=" * 50
echo "📋 SUMMARY"
echo "=" * 50

# Check which files have data
for file in user_profiles_sample.json events_sample.json cohorts_sample.json segmentation_sample.json; do
    if [ -s "$file" ]; then
        size=$(wc -c < "$file")
        echo "✅ $file: $size bytes"
    else
        echo "❌ $file: No data"
    fi
done

echo ""
echo "🔍 To explore the data further, check the generated JSON files:"
echo "   - user_profiles_sample.json: User properties and structure"
echo "   - events_sample.json: Available events and their data"
echo "   - cohorts_sample.json: Available user cohorts"
echo "   - segmentation_sample.json: Segmentation capabilities"

echo ""
echo "💡 Next steps:"
echo "   1. Review the JSON files to understand your data structure"
echo "   2. Identify the actual event names in your Mixpanel project"
echo "   3. Update the MixpanelClient with the correct event names"
echo "   4. Test the Python client with real event names"
