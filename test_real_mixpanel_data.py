#!/usr/bin/env python3
"""
Test script to verify we can fetch real Mixpanel data using the Python client.

Based on the API exploration, we know these events have data:
- extension_page_view: 165M+ events, 1.7M+ unique users
- heartbeat: 2M+ events, 1.6M+ unique users  
- page_view: 1.5M+ events, 231K+ unique users
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from mixpanel.mixpanel_client import MixpanelClient

def test_real_data():
    """Test fetching real data from Mixpanel."""
    print("🚀 Testing Mixpanel Client with Real Data")
    print("=" * 50)
    
    # Initialize client with real credentials
    try:
        client = MixpanelClient(
            project_id=os.getenv('MIXPANEL_PROJECT_ID'),
            service_account_username=os.getenv('MIXPANEL_SERVICE_USERNAME'),
            service_account_secret=os.getenv('MIXPANEL_SERVICE_SECRET'),
            workspace_id=os.getenv('MIXPANEL_WORKSPACE_ID')
        )
        print("✅ Client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return
    
    # Test connection
    print("\n🔍 Testing connection...")
    if not client.test_connection():
        print("❌ Connection failed")
        return
    print("✅ Connection successful")
    
    # Use yesterday for testing
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    print(f"\n📅 Testing with date range: {yesterday}")
    
    # Test 1: Core extension metrics with real events
    print("\n1️⃣ Testing core extension metrics...")
    try:
        # Use the actual events we discovered
        real_events = ["extension_page_view", "heartbeat", "page_view"]
        
        events_data = client.get_events_data(
            events=real_events,
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day"
        )
        
        if events_data and events_data.get('data'):
            print("✅ Core metrics fetched successfully!")
            values = events_data['data'].get('values', {})
            for event, data in values.items():
                total = sum(data.values()) if data else 0
                print(f"   📊 {event}: {total:,} events")
        else:
            print("⚠️ No core metrics data returned")
            
    except Exception as e:
        print(f"❌ Core metrics error: {e}")
    
    # Test 2: Unique users for real events
    print("\n2️⃣ Testing unique users...")
    try:
        unique_data = client.get_events_data(
            events=["extension_page_view", "heartbeat"],
            from_date=yesterday,
            to_date=yesterday,
            event_type="unique",
            unit="day"
        )
        
        if unique_data and unique_data.get('data'):
            print("✅ Unique users fetched successfully!")
            values = unique_data['data'].get('values', {})
            for event, data in values.items():
                total = sum(data.values()) if data else 0
                print(f"   👥 {event}: {total:,} unique users")
        else:
            print("⚠️ No unique users data returned")
            
    except Exception as e:
        print(f"❌ Unique users error: {e}")
    
    # Test 3: User profiles (small sample)
    print("\n3️⃣ Testing user profiles...")
    try:
        users = client.get_user_profiles(
            output_properties=["$email", "$first_name", "$last_seen", "$created_at", "$onboarded_at"],
            page=0
        )
        
        if users and users.get('results'):
            print(f"✅ User profiles fetched: {len(users['results'])} users")
            print(f"   📈 Total users in project: {users.get('total', 'unknown')}")
            
            # Show sample user properties
            if users['results']:
                sample_user = users['results'][0]
                props = sample_user.get('$properties', {})
                print(f"   👤 Sample user properties: {list(props.keys())[:10]}...")
        else:
            print("⚠️ No user profiles returned")
            
    except Exception as e:
        print(f"❌ User profiles error: {e}")
    
    # Test 4: Segmentation by browser (using real event)
    print("\n4️⃣ Testing segmentation...")
    try:
        segmentation = client.get_segmentation_data(
            event="extension_page_view",  # Our top event
            from_date=yesterday,
            to_date=yesterday,
            segment_property="$browser",  # Standard Mixpanel property
            event_type="unique",
            limit=5
        )
        
        if segmentation and segmentation.get('data'):
            print("✅ Segmentation fetched successfully!")
            values = segmentation['data'].get('values', {})
            print(f"   🌐 Top browsers: {list(values.keys())[:5]}")
        else:
            print("⚠️ No segmentation data returned")
            
    except Exception as e:
        print(f"❌ Segmentation error: {e}")
    
    # Test 5: Available cohorts
    print("\n5️⃣ Testing cohorts...")
    try:
        cohorts = client.get_cohorts_list()
        
        if cohorts and cohorts.get('results'):
            print(f"✅ Cohorts fetched: {len(cohorts['results'])} cohorts available")
            for cohort in cohorts['results'][:3]:
                print(f"   👥 {cohort['name']}: {cohort['count']} users")
        else:
            print("⚠️ No cohorts returned")
            
    except Exception as e:
        print(f"❌ Cohorts error: {e}")
    
    # Test 6: Business method - DAU with real events
    print("\n6️⃣ Testing business method (DAU)...")
    try:
        dau_data = client.get_dau_mau_metrics(
            from_date=yesterday,
            to_date=yesterday,
            metric_type="DAU"
        )
        
        if dau_data:
            print("✅ DAU business method successful!")
            print(f"   📊 Data keys: {list(dau_data.keys())}")
        else:
            print("⚠️ No DAU data returned")
            
    except Exception as e:
        print(f"❌ DAU business method error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Real data test completed!")
    print("\n💡 Next steps:")
    print("   1. All tests passed? ✅ Ready to integrate into DAG")
    print("   2. Some tests failed? 🔧 Debug specific methods")
    print("   3. Update event names in client based on your actual events")

if __name__ == "__main__":
    test_real_data()
