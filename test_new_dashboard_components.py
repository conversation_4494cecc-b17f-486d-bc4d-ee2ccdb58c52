#!/usr/bin/env python3
"""
Test script for new Mixpanel dashboard components.

This script validates that all 5 new dashboard components work correctly:
1. Week 9 Retention (Churn)
2. Safari Extension Users Since Launch (already working)
3. Weekly ACTIVE Safari Extension Users (already working)
4. Enabled Permissions % First-Time Run
5. New Site Activation Rate Broken Down by Site

Run this before deploying to production to ensure everything works.
"""

import os
import sys
import json
from datetime import datetime, timedel<PERSON>

# Add the dags directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags'))

from dependencies.mixpanel.mixpanel_client import MixpanelClient
from dependencies.mixpanel.mixpanel_transform import (
    transform_site_activation_rates,
    transform_onboarding_funnel,
)

def test_mixpanel_connection():
    """Test basic Mixpanel API connection."""
    print("🔍 Testing Mixpanel API connection...")
    
    # Get credentials from environment
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    username = os.getenv("MIXPANEL_SERVICE_USERNAME") 
    secret = os.getenv("MIXPANEL_SERVICE_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")
    
    if not all([project_id, username, secret]):
        print("❌ Missing Mixpanel credentials in environment variables")
        return None
    
    # Initialize client
    try:
        client = MixpanelClient(
            project_id=project_id,
            service_account_username=username,
            service_account_secret=secret,
            workspace_id=workspace_id,
            respect_rate_limits=True
        )
        print(f"✅ Client initialized for project: {project_id}")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return None

def test_new_dashboard_components(client):
    """Test all 5 new dashboard components."""
    print("\n🎯 Testing New Dashboard Components...")
    print("=" * 60)
    
    # Use recent date with data
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    week_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    
    results = {}
    
    # Component 1: Safari Extension Users (Enhanced)
    print("\n1️⃣ Testing Safari Extension Users...")
    try:
        safari_users = client.get_safari_extension_users(yesterday, yesterday)
        if safari_users and safari_users.get("safari_extension_users"):
            user_count = safari_users["safari_extension_users"].get(yesterday, 0)
            print(f"✅ Safari Extension Users: {user_count:,} users on {yesterday}")
            results["safari_users"] = "success"
        else:
            print("⚠️ Safari Extension Users: No data returned")
            results["safari_users"] = "no_data"
    except Exception as e:
        print(f"❌ Safari Extension Users error: {e}")
        results["safari_users"] = f"error: {e}"
    
    # Component 2: Site Activation Rates
    print("\n2️⃣ Testing Site Activation Rates...")
    try:
        site_activation = client.get_site_activation_rates(yesterday, yesterday)
        if site_activation and site_activation.get("site_activation_rates"):
            rates = site_activation["site_activation_rates"]
            print(f"✅ Site Activation Rates: Found {len(rates)} sites")
            
            # Show top 3 sites
            sorted_sites = sorted(rates.items(), key=lambda x: x[1]["activation_rate"], reverse=True)[:3]
            for hostname, metrics in sorted_sites:
                rate = metrics["activation_rate"]
                shown = metrics["shown"]
                clicked = metrics["clicked"]
                print(f"   🌐 {hostname}: {rate}% ({clicked}/{shown})")
            
            results["site_activation"] = "success"
        else:
            print("⚠️ Site Activation Rates: No data returned")
            results["site_activation"] = "no_data"
    except Exception as e:
        print(f"❌ Site Activation Rates error: {e}")
        results["site_activation"] = f"error: {e}"
    
    # Component 3: Onboarding Funnel Analysis
    print("\n3️⃣ Testing Onboarding Funnel Analysis...")
    try:
        funnel_analysis = client.get_onboarding_funnel_analysis(yesterday, yesterday)
        if funnel_analysis and funnel_analysis.get("funnel_analysis"):
            analysis = funnel_analysis["funnel_analysis"]
            step1 = analysis.get("step1_total", 0)
            step2 = analysis.get("step2_total", 0)
            conversion = analysis.get("overall_conversion_rate", 0)
            
            print(f"✅ Onboarding Funnel Analysis:")
            print(f"   📊 Step 1 (almost-finished): {step1:,} users")
            print(f"   📊 Step 2 (phia.com): {step2:,} users")
            print(f"   📈 Conversion Rate: {conversion}%")
            
            results["onboarding_funnel"] = "success"
        else:
            print("⚠️ Onboarding Funnel Analysis: No data returned")
            results["onboarding_funnel"] = "no_data"
    except Exception as e:
        print(f"❌ Onboarding Funnel Analysis error: {e}")
        results["onboarding_funnel"] = f"error: {e}"
    
    # Component 4: Weekly Retention Cohort Analysis
    print("\n4️⃣ Testing Weekly Retention Cohort Analysis...")
    try:
        # Test with a cohort from 2 weeks ago
        cohort_start = (datetime.now() - timedelta(days=14)).strftime("%Y-%m-%d")
        retention_analysis = client.get_weekly_retention_cohort_analysis(
            cohort_start_date=cohort_start,
            analysis_weeks=3  # Just test 3 weeks for speed
        )
        
        if retention_analysis and retention_analysis.get("cohort_analysis"):
            analysis = retention_analysis["cohort_analysis"]
            cohort_size = analysis.get("cohort_size", 0)
            retention_curve = analysis.get("retention_curve", [])
            
            print(f"✅ Weekly Retention Analysis:")
            print(f"   👥 Cohort Size: {cohort_size:,} users")
            print(f"   📈 Retention Curve:")
            
            for week_data in retention_curve[:3]:  # Show first 3 weeks
                week = week_data["week"]
                rate = week_data["retention_rate"]
                print(f"      Week {week}: {rate}%")
            
            results["retention_analysis"] = "success"
        else:
            print("⚠️ Weekly Retention Analysis: No data returned")
            results["retention_analysis"] = "no_data"
    except Exception as e:
        print(f"❌ Weekly Retention Analysis error: {e}")
        results["retention_analysis"] = f"error: {e}"
    
    # Component 5: Test Data Transformations
    print("\n5️⃣ Testing Data Transformations...")
    try:
        # Test site activation transformation
        if "site_activation" in results and results["site_activation"] == "success":
            site_data = client.get_site_activation_rates(yesterday, yesterday)
            transformed_site = transform_site_activation_rates(site_data, yesterday)
            print(f"✅ Site Activation Transform: {len(transformed_site)} records")
        
        # Test onboarding funnel transformation
        if "onboarding_funnel" in results and results["onboarding_funnel"] == "success":
            funnel_data = client.get_onboarding_funnel_analysis(yesterday, yesterday)
            transformed_funnel = transform_onboarding_funnel(funnel_data, yesterday)
            print(f"✅ Onboarding Funnel Transform: {len(transformed_funnel)} records")
        
        results["transformations"] = "success"
    except Exception as e:
        print(f"❌ Data Transformations error: {e}")
        results["transformations"] = f"error: {e}"
    
    return results

def test_comprehensive_data_fetch(client):
    """Test the comprehensive data fetch with new components."""
    print("\n🚀 Testing Comprehensive Data Fetch...")
    
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    try:
        # Test the updated optimized method
        all_data = client.get_all_analytics_data_optimized(
            from_date=yesterday,
            to_date=yesterday,
            include_user_data=False,  # Skip for speed
        )
        
        if all_data and all_data.get("success"):
            print("✅ Comprehensive data fetch successful!")
            
            data_keys = list(all_data.get("data", {}).keys())
            api_calls = all_data.get("api_calls_used", 0)
            
            print(f"📊 Data components: {len(data_keys)}")
            print(f"🔄 API calls used: {api_calls}")
            print(f"📋 Components collected:")
            
            for key in data_keys:
                print(f"   - {key}")
            
            # Check for new components
            new_components = [
                "safari_extension_metrics",
                "site_activation_rates", 
                "onboarding_funnel"
            ]
            
            found_new = [comp for comp in new_components if comp in data_keys]
            print(f"\n🎯 New components found: {len(found_new)}/{len(new_components)}")
            
            for comp in found_new:
                print(f"   ✅ {comp}")
            
            missing_new = [comp for comp in new_components if comp not in data_keys]
            for comp in missing_new:
                print(f"   ❌ {comp}")
            
            return True
        else:
            print(f"❌ Comprehensive data fetch failed: {all_data.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Comprehensive data fetch error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing New Mixpanel Dashboard Components")
    print("=" * 60)
    
    # Test connection
    client = test_mixpanel_connection()
    if not client:
        return
    
    # Test individual components
    component_results = test_new_dashboard_components(client)
    
    # Test comprehensive fetch
    comprehensive_success = test_comprehensive_data_fetch(client)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    successful_components = sum(1 for result in component_results.values() if result == "success")
    total_components = len(component_results)
    
    print(f"✅ Individual Components: {successful_components}/{total_components} successful")
    print(f"✅ Comprehensive Fetch: {'Success' if comprehensive_success else 'Failed'}")
    
    if successful_components == total_components and comprehensive_success:
        print("\n🎉 ALL TESTS PASSED! Ready for deployment!")
    else:
        print("\n⚠️ Some tests failed. Review errors before deployment.")
    
    print("\n📊 Component Details:")
    for component, result in component_results.items():
        status = "✅" if result == "success" else "❌" if "error" in result else "⚠️"
        print(f"   {status} {component}: {result}")

if __name__ == "__main__":
    main()
