#!/usr/bin/env python3
"""
Test script for MixpanelClient to verify data fetching capabilities.

This script tests the Mixpanel client with real credentials to ensure
we can fetch all required data types before integrating into the DAG.
"""

import os
import sys
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the dependencies directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "dependencies"))

from mixpanel.mixpanel_client import MixpanelClient


def test_mixpanel_connection():
    """Test basic Mixpanel API connection."""
    print("🔍 Testing Mixpanel API connection...")

    # Get credentials from environment variables
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    username = os.getenv("MIXPANEL_SERVICE_USERNAME")
    secret = os.getenv("MIXPANEL_SERVICE_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")  # Optional

    if not all([project_id, username, secret]):
        print("❌ Missing required environment variables:")
        print("   - MIXPANEL_PROJECT_ID")
        print("   - MIXPANEL_SERVICE_USERNAME")
        print("   - MIXPANEL_SERVICE_SECRET")
        print("   - MIXPANEL_WORKSPACE_ID (optional)")
        return None

    # Initialize client
    try:
        client = MixpanelClient(
            project_id=project_id,
            service_account_username=username,
            service_account_secret=secret,
            workspace_id=workspace_id,
        )
        print(f"✅ Client initialized for project: {project_id}")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return None


def test_connection_health(client):
    """Test if we can connect to Mixpanel API."""
    print("\n🏥 Testing connection health...")

    try:
        is_connected = client.test_connection()
        if is_connected:
            print("✅ Connection test passed!")
            return True
        else:
            print("❌ Connection test failed!")
            return False
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        return False


def test_individual_data_types(client):
    """Test each data type individually."""
    print("\n📊 Testing individual data types...")

    # Use yesterday's date for testing
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    week_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

    results = {}

    # Test 1: Core extension metrics
    print("\n1️⃣ Testing core extension metrics...")
    try:
        core_metrics = client.get_core_extension_metrics(yesterday, yesterday)
        if core_metrics:
            print(f"✅ Core metrics: Found data")
            results["core_metrics"] = "success"
        else:
            print("⚠️ Core metrics: No data returned")
            results["core_metrics"] = "no_data"
    except Exception as e:
        print(f"❌ Core metrics error: {e}")
        results["core_metrics"] = f"error: {e}"

    # Test 2: DAU metrics
    print("\n2️⃣ Testing DAU metrics...")
    try:
        dau_data = client.get_dau_mau_metrics(yesterday, yesterday, "DAU")
        if dau_data:
            print(f"✅ DAU metrics: Found data")
            results["dau"] = "success"
        else:
            print("⚠️ DAU metrics: No data returned")
            results["dau"] = "no_data"
    except Exception as e:
        print(f"❌ DAU metrics error: {e}")
        results["dau"] = f"error: {e}"

    # Test 3: User profiles (limit to 10 for testing)
    print("\n3️⃣ Testing user profiles...")
    try:
        # Test with a small sample first
        user_data = client.get_user_profiles(
            output_properties=["$email", "$first_name", "$last_seen"], page=0
        )
        if user_data and user_data.get("results"):
            print(f"✅ User profiles: Found {len(user_data['results'])} users")
            results["user_profiles"] = "success"
        else:
            print("⚠️ User profiles: No data returned")
            results["user_profiles"] = "no_data"
    except Exception as e:
        print(f"❌ User profiles error: {e}")
        results["user_profiles"] = f"error: {e}"

    # Test 4: Onboarding conversion
    print("\n4️⃣ Testing onboarding conversion...")
    try:
        onboarding = client.get_onboarding_conversion(week_ago, yesterday)
        if onboarding:
            print(f"✅ Onboarding: Found data")
            results["onboarding"] = "success"
        else:
            print("⚠️ Onboarding: No data returned")
            results["onboarding"] = "no_data"
    except Exception as e:
        print(f"❌ Onboarding error: {e}")
        results["onboarding"] = f"error: {e}"

    # Test 5: Site activation
    print("\n5️⃣ Testing site activation...")
    try:
        site_activation = client.get_activation_rate_per_site(week_ago, yesterday)
        if site_activation:
            print(f"✅ Site activation: Found data")
            results["site_activation"] = "success"
        else:
            print("⚠️ Site activation: No data returned")
            results["site_activation"] = "no_data"
    except Exception as e:
        print(f"❌ Site activation error: {e}")
        results["site_activation"] = f"error: {e}"

    return results


def test_comprehensive_data(client):
    """Test the comprehensive data collection method."""
    print("\n🎯 Testing comprehensive data collection...")

    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    try:
        # Test with limited scope first (skip slow operations)
        all_data = client.get_all_analytics_data(
            from_date=yesterday,
            to_date=yesterday,
            include_user_data=False,  # Skip for speed
            include_retention=False,  # Skip for speed
        )

        if all_data and all_data.get("success"):
            print("✅ Comprehensive data collection successful!")
            print(f"📈 Data types collected: {list(all_data.get('data', {}).keys())}")
            return True
        else:
            print(
                f"❌ Comprehensive data collection failed: {all_data.get('error', 'Unknown error')}"
            )
            return False

    except Exception as e:
        print(f"❌ Comprehensive data error: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Starting Mixpanel Client Test")
    print("=" * 50)

    # Step 1: Initialize client
    client = test_mixpanel_connection()
    if not client:
        print("\n❌ Cannot proceed without valid client")
        return

    # Step 2: Test connection
    if not test_connection_health(client):
        print("\n❌ Cannot proceed without valid connection")
        return

    # Step 3: Test individual data types
    individual_results = test_individual_data_types(client)

    # Step 4: Test comprehensive collection
    comprehensive_success = test_comprehensive_data(client)

    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)

    for data_type, result in individual_results.items():
        status = "✅" if result == "success" else "⚠️" if result == "no_data" else "❌"
        print(f"{status} {data_type}: {result}")

    overall_status = "✅" if comprehensive_success else "❌"
    print(
        f"{overall_status} Comprehensive collection: {'Success' if comprehensive_success else 'Failed'}"
    )

    print("\n🎉 Test completed!")


if __name__ == "__main__":
    main()
