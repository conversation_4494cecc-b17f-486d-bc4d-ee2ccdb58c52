"""
Data transformation functions for Strackr transactions.
Converts Strackr API responses to normalized transaction schema.
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    from .schema import NormalizedTransaction, validate_transaction
    from .constants import PLATFORM_STRACKR
except ImportError:
    # Handle direct import when not running as package
    from schema import NormalizedTransaction, validate_transaction
    from constants import PLATFORM_STRACKR

logger = logging.getLogger(__name__)


def transform_strackr_transaction(
    strackr_data: Dict[str, Any],
) -> Optional[NormalizedTransaction]:
    """
    Transform a single Strackr transaction to normalized schema.

    Args:
        strackr_data: Raw transaction data from Strackr API

    Returns:
        NormalizedTransaction instance or None if transformation fails
    """
    try:
        # Extract core identifiers
        source_id = strackr_data["id"]
        transaction_id = f"{PLATFORM_STRACKR}_{source_id}"

        # Parse dates
        transaction_date = _parse_datetime(strackr_data["sold_at"])
        # For transactions endpoint, use clicked_at or sold_at as created_date since created_at may not exist
        created_date = (
            _parse_datetime(strackr_data.get("clicked_at")) or transaction_date
        )
        last_updated = (
            _parse_datetime(strackr_data.get("status_updated_at")) or created_date
        )

        # Extract financial data (field names changed for transactions endpoint)
        currency = strackr_data["currency"]
        order_amount = float(
            strackr_data["order_amount"]
        )  # Changed from expected_order_amount
        commission_amount = float(
            strackr_data["revenue"]
        )  # Changed from expected_revenue

        # Final amounts (transactions endpoint may not have separate final amounts)
        # For transactions, the order_amount and revenue are typically the final amounts
        final_order_amount = None
        final_commission_amount = None
        # Check if there are separate final amounts (may not exist in transactions endpoint)
        if strackr_data.get("final_order_amount") is not None:
            final_order_amount = float(strackr_data["final_order_amount"])
        if strackr_data.get("final_revenue") is not None:
            final_commission_amount = float(strackr_data["final_revenue"])

        # Extract merchant/network info
        network_name = strackr_data["network_name"]
        merchant_name = strackr_data.get("advertiser_name") or "Unknown"
        merchant_id = strackr_data.get("advertiser_id")
        connection_name = strackr_data.get("connection_name")

        # Extract status information
        status = _normalize_status(strackr_data["status_id"])
        # For transactions endpoint, type_id may not exist or may be different
        transaction_type = strackr_data.get("type_id") or "transaction"

        # Extract decline reason if present
        decline_reason = None
        if strackr_data.get("reason") and isinstance(strackr_data["reason"], dict):
            decline_reason = strackr_data["reason"].get("name")

        # Extract additional metadata
        channel_name = strackr_data.get("channel_name")
        comments = strackr_data.get("comment")

        # Build custom fields with Strackr-specific data
        custom_fields = {
            "network_id": strackr_data.get("network_id"),
            "connection_id": strackr_data.get("connection_id"),
            "source_currency": strackr_data.get("source_currency"),
            "customs": strackr_data.get("customs", []),
            "type_id": strackr_data.get("type_id"),
        }

        # Create normalized transaction
        normalized = NormalizedTransaction(
            transaction_id=transaction_id,
            platform=PLATFORM_STRACKR,
            source_transaction_id=source_id,
            currency=currency,
            order_amount=order_amount,
            commission_amount=commission_amount,
            final_order_amount=final_order_amount,
            final_commission_amount=final_commission_amount,
            order_id=strackr_data.get("order_id")
            or source_id,  # Use transaction ID as fallback for null order_id
            customer_id=strackr_data.get("customer_id"),
            transaction_date=transaction_date,
            created_date=created_date,
            network_name=network_name,
            merchant_name=merchant_name,
            merchant_id=merchant_id,
            connection_name=connection_name,
            status=status,
            transaction_type=transaction_type,
            decline_reason=decline_reason,
            channel_name=channel_name,
            custom_fields=custom_fields,
            comments=comments,
            last_updated=last_updated,
        )

        # Validate the normalized transaction
        validation_errors = validate_transaction(normalized)
        if validation_errors:
            logger.warning(
                f"Validation errors for transaction {source_id}: {validation_errors}"
            )
            return None

        return normalized

    except Exception as e:
        logger.error(
            f"Failed to transform Strackr transaction {strackr_data.get('id', 'unknown')}: {str(e)}"
        )
        return None


def transform_strackr_transactions(
    strackr_transactions: List[Dict[str, Any]],
) -> List[NormalizedTransaction]:
    """
    Transform a list of Strackr transactions to normalized schema.

    Args:
        strackr_transactions: List of raw transaction data from Strackr API

    Returns:
        List of NormalizedTransaction instances (excludes failed transformations)
    """
    logger.info(f"Transforming {len(strackr_transactions)} Strackr transactions")

    normalized_transactions = []
    failed_count = 0

    for strackr_data in strackr_transactions:
        normalized = transform_strackr_transaction(strackr_data)
        if normalized:
            normalized_transactions.append(normalized)
        else:
            failed_count += 1

    logger.info(f"Successfully transformed {len(normalized_transactions)} transactions")
    if failed_count > 0:
        logger.warning(f"Failed to transform {failed_count} transactions")

    return normalized_transactions


def _parse_datetime(date_string: Optional[str]) -> Optional[datetime]:
    """
    Parse datetime string from Strackr API.

    Args:
        date_string: ISO datetime string from API

    Returns:
        datetime object or None if parsing fails
    """
    if not date_string:
        return None

    try:
        # Handle different datetime formats from Strackr
        if date_string.endswith("Z"):
            return datetime.fromisoformat(date_string.replace("Z", "+00:00"))
        elif "+" in date_string or date_string.endswith("00"):
            return datetime.fromisoformat(date_string)
        else:
            # Assume UTC if no timezone info
            return datetime.fromisoformat(date_string + "+00:00")
    except Exception as e:
        logger.error(f"Failed to parse datetime '{date_string}': {str(e)}")
        return None


def _normalize_status(strackr_status: str) -> str:
    """
    Normalize Strackr status to standard status values.

    Args:
        strackr_status: Status from Strackr API

    Returns:
        Normalized status string
    """
    status_mapping = {
        "pending": "pending",
        "confirmed": "confirmed",
        "declined": "declined",
    }

    return status_mapping.get(strackr_status.lower(), strackr_status.lower())


def get_transformation_summary(
    original_count: int, transformed_transactions: List[NormalizedTransaction]
) -> Dict[str, Any]:
    """
    Generate summary statistics for the transformation process.

    Args:
        original_count: Number of original transactions from API
        transformed_transactions: List of successfully transformed transactions

    Returns:
        Dictionary with transformation summary statistics
    """
    transformed_count = len(transformed_transactions)
    failed_count = original_count - transformed_count

    # Calculate status distribution
    status_counts = {}
    currency_counts = {}
    total_commission = 0.0

    for transaction in transformed_transactions:
        # Status distribution
        status = transaction.status
        status_counts[status] = status_counts.get(status, 0) + 1

        # Currency distribution
        currency = transaction.currency
        currency_counts[currency] = currency_counts.get(currency, 0) + 1

        # Total commission
        total_commission += transaction.commission_amount

    return {
        "original_count": original_count,
        "transformed_count": transformed_count,
        "failed_count": failed_count,
        "success_rate": (
            (transformed_count / original_count * 100) if original_count > 0 else 0
        ),
        "status_distribution": status_counts,
        "currency_distribution": currency_counts,
        "total_commission_amount": total_commission,
        "transformation_timestamp": datetime.utcnow().isoformat(),
    }
