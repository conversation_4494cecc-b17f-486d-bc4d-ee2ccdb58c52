"""
Common utility functions for transaction reports processing.
Includes date handling, GCS operations, and data validation utilities.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from google.cloud import storage
from .constants import GCS_BUCKET_NAME
from .schema import NormalizedTransaction

logger = logging.getLogger(__name__)


def get_date_range_for_yesterday() -> tuple[str, str]:
    """
    Get ISO formatted date range for yesterday (full day).

    Returns:
        Tuple of (start_date, end_date) in ISO format
    """
    yesterday = datetime.utcnow() - timedelta(days=1)
    start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
    # For Strackr API, end date should also be at 00:00:00 (start of next day)
    end_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(
        days=1
    )

    return (
        start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
        end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
    )


def get_date_range_for_days_back(days_back: int) -> tuple[str, str]:
    """
    Get ISO formatted date range for N days back from today.

    Args:
        days_back: Number of days back from today

    Returns:
        Tuple of (start_date, end_date) in ISO format
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days_back)

    return (
        start_date.strftime("%Y-%m-%dT00:00:00Z"),
        end_date.strftime("%Y-%m-%dT00:00:00Z"),
    )


def format_date_for_filename(date_obj: datetime) -> str:
    """
    Format datetime for use in filenames.

    Args:
        date_obj: datetime object

    Returns:
        Date string formatted for filenames (YYYY-MM-DD)
    """
    return date_obj.strftime("%Y-%m-%d")


def store_json_to_gcs(
    data: Any, bucket_name: str, blob_path: str, project_id: Optional[str] = None
) -> bool:
    """
    Store JSON data to Google Cloud Storage.

    Args:
        data: Data to store (will be JSON serialized)
        bucket_name: GCS bucket name
        blob_path: Path within bucket
        project_id: GCP project ID (optional)

    Returns:
        True if successful, False otherwise
    """
    try:
        client = storage.Client(project=project_id)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_path)

        json_data = json.dumps(data, indent=2, default=str)
        blob.upload_from_string(json_data, content_type="application/json")

        logger.info(f"Successfully stored data to gs://{bucket_name}/{blob_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to store data to GCS: {str(e)}")
        return False


def load_json_from_gcs(
    bucket_name: str, blob_path: str, project_id: Optional[str] = None
) -> Optional[Any]:
    """
    Load JSON data from Google Cloud Storage.

    Args:
        bucket_name: GCS bucket name
        blob_path: Path within bucket
        project_id: GCP project ID (optional)

    Returns:
        Loaded data or None if failed
    """
    try:
        client = storage.Client(project=project_id)
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_path)

        if not blob.exists():
            logger.warning(f"Blob does not exist: gs://{bucket_name}/{blob_path}")
            return None

        json_data = blob.download_as_text()
        data = json.loads(json_data)

        logger.info(f"Successfully loaded data from gs://{bucket_name}/{blob_path}")
        return data

    except Exception as e:
        logger.error(f"Failed to load data from GCS: {str(e)}")
        return None


def store_transactions_to_gcs(
    transactions: List[NormalizedTransaction],
    platform: str,
    date_str: str,
    data_type: str = "processed",
) -> Optional[str]:
    """
    Store normalized transactions to GCS with standardized naming.

    Args:
        transactions: List of normalized transactions
        platform: Platform name (strackr, shopmy)
        date_str: Date string for filename
        data_type: Type of data (raw, processed)

    Returns:
        GCS path if successful, None otherwise
    """
    try:
        # Convert transactions to dictionaries
        transaction_dicts = [t.to_dict() for t in transactions]

        # Build GCS path
        blob_path = f"{platform}/{data_type}/{date_str}_transactions.json"

        success = store_json_to_gcs(
            data=transaction_dicts, bucket_name=GCS_BUCKET_NAME, blob_path=blob_path
        )

        if success:
            return f"gs://{GCS_BUCKET_NAME}/{blob_path}"
        else:
            return None

    except Exception as e:
        logger.error(f"Failed to store transactions to GCS: {str(e)}")
        return None


def validate_transaction_data_quality(
    transactions: List[NormalizedTransaction], platform: str
) -> Dict[str, Any]:
    """
    Validate data quality of normalized transactions.

    Args:
        transactions: List of normalized transactions
        platform: Platform name for context

    Returns:
        Dictionary with validation results and metrics
    """
    if not transactions:
        return {
            "is_valid": False,
            "error": "No transactions provided",
            "transaction_count": 0,
        }

    validation_results = {
        "is_valid": True,
        "transaction_count": len(transactions),
        "platform": platform,
        "validation_timestamp": datetime.utcnow().isoformat(),
        "errors": [],
        "warnings": [],
        "metrics": {},
    }

    # Basic count validation
    if len(transactions) == 0:
        validation_results["errors"].append("No transactions found")
        validation_results["is_valid"] = False

    # Calculate metrics
    total_commission = sum(t.commission_amount for t in transactions)
    total_order_value = sum(t.order_amount for t in transactions)

    status_counts = {}
    currency_counts = {}

    for transaction in transactions:
        # Count statuses
        status = transaction.status
        status_counts[status] = status_counts.get(status, 0) + 1

        # Count currencies
        currency = transaction.currency
        currency_counts[currency] = currency_counts.get(currency, 0) + 1

    validation_results["metrics"] = {
        "total_commission_amount": total_commission,
        "total_order_value": total_order_value,
        "average_commission": total_commission / len(transactions),
        "average_order_value": total_order_value / len(transactions),
        "status_distribution": status_counts,
        "currency_distribution": currency_counts,
    }

    # Data quality checks
    if total_commission <= 0:
        validation_results["warnings"].append(
            "Total commission amount is zero or negative"
        )

    if total_order_value <= 0:
        validation_results["warnings"].append("Total order value is zero or negative")

    # Check for missing required data
    missing_order_ids = sum(1 for t in transactions if not t.order_id)
    if missing_order_ids > 0:
        validation_results["warnings"].append(
            f"{missing_order_ids} transactions missing order_id"
        )

    missing_merchants = sum(1 for t in transactions if not t.merchant_name)
    if missing_merchants > 0:
        validation_results["warnings"].append(
            f"{missing_merchants} transactions missing merchant_name"
        )

    return validation_results


def create_processing_summary(
    platform: str,
    start_date: str,
    end_date: str,
    raw_count: int,
    processed_count: int,
    validation_results: Dict[str, Any],
    processing_time_seconds: float,
) -> Dict[str, Any]:
    """
    Create a comprehensive processing summary.

    Args:
        platform: Platform name
        start_date: Processing start date
        end_date: Processing end date
        raw_count: Number of raw transactions
        processed_count: Number of successfully processed transactions
        validation_results: Results from data quality validation
        processing_time_seconds: Total processing time

    Returns:
        Dictionary with processing summary
    """
    return {
        "platform": platform,
        "processing_timestamp": datetime.utcnow().isoformat(),
        "date_range": {"start_date": start_date, "end_date": end_date},
        "counts": {
            "raw_transactions": raw_count,
            "processed_transactions": processed_count,
            "failed_transactions": raw_count - processed_count,
            "success_rate": (processed_count / raw_count * 100) if raw_count > 0 else 0,
        },
        "processing_time_seconds": processing_time_seconds,
        "data_quality": validation_results,
    }
