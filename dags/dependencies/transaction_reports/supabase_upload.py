"""
Supabase upload utilities for normalized transaction data.
Handles batch uploads, conflict resolution, and data validation.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..supabase_client import SupabaseClient, SupabaseClientLocal
from .schema import NormalizedTransaction, validate_transaction

logger = logging.getLogger(__name__)


class TransactionUploader:
    """
    Handles uploading normalized transactions to Supabase with validation and error handling.
    """

    def __init__(self, supabase_client: Optional[SupabaseClient] = None):
        """
        Initialize the uploader.
        
        Args:
            supabase_client: SupabaseClient instance (creates local client if None)
        """
        self.client = supabase_client or SupabaseClientLocal()
        
    def upload_transactions(
        self,
        transactions: List[NormalizedTransaction],
        validate_before_upload: bool = True,
        batch_size: int = 100,
        on_conflict: str = "ignore"
    ) -> Dict[str, Any]:
        """
        Upload normalized transactions to Supabase.
        
        Args:
            transactions: List of NormalizedTransaction objects
            validate_before_upload: Whether to validate transactions before upload
            batch_size: Number of transactions per batch
            on_conflict: Conflict resolution strategy ("ignore", "update", "error")
            
        Returns:
            Dictionary with upload results and statistics
        """
        if not transactions:
            logger.info("No transactions to upload")
            return {
                "success": True,
                "total_transactions": 0,
                "valid_transactions": 0,
                "uploaded_transactions": 0,
                "validation_errors": 0,
                "upload_errors": 0,
                "errors": []
            }
        
        logger.info(f"Starting upload of {len(transactions)} transactions")
        
        # Validate transactions if requested
        valid_transactions = transactions
        validation_errors = 0
        validation_error_details = []
        
        if validate_before_upload:
            valid_transactions, validation_errors, validation_error_details = self._validate_transactions(transactions)
        
        if not valid_transactions:
            logger.warning("No valid transactions to upload after validation")
            return {
                "success": False,
                "total_transactions": len(transactions),
                "valid_transactions": 0,
                "uploaded_transactions": 0,
                "validation_errors": validation_errors,
                "upload_errors": 0,
                "errors": validation_error_details
            }
        
        # Upload to Supabase
        upload_result = self.client.insert_transactions(
            valid_transactions,
            batch_size=batch_size,
            on_conflict=on_conflict
        )
        
        # Compile final results
        result = {
            "success": upload_result["success"] and validation_errors == 0,
            "total_transactions": len(transactions),
            "valid_transactions": len(valid_transactions),
            "uploaded_transactions": upload_result["inserted_count"],
            "validation_errors": validation_errors,
            "upload_errors": upload_result["error_count"],
            "errors": validation_error_details + upload_result["errors"]
        }
        
        logger.info(f"Upload complete: {result['uploaded_transactions']}/{result['total_transactions']} transactions uploaded")
        
        return result

    def upload_daily_transactions(
        self,
        platform: str,
        transactions: List[NormalizedTransaction],
        date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Upload transactions for a specific day with deduplication.
        
        Args:
            platform: Platform name ('strackr' or 'shopmy')
            transactions: List of transactions for the day
            date: Date for the transactions (defaults to yesterday)
            
        Returns:
            Upload results dictionary
        """
        if date is None:
            date = datetime.now() - timedelta(days=1)
        
        date_str = date.strftime("%Y-%m-%d")
        logger.info(f"Uploading {len(transactions)} {platform} transactions for {date_str}")
        
        # Filter transactions to only include the specified date
        filtered_transactions = [
            tx for tx in transactions 
            if tx.transaction_date.date() == date.date()
        ]
        
        if len(filtered_transactions) != len(transactions):
            logger.warning(f"Filtered {len(transactions) - len(filtered_transactions)} transactions outside target date")
        
        # Upload with conflict resolution (update existing records)
        return self.upload_transactions(
            filtered_transactions,
            validate_before_upload=True,
            batch_size=100,
            on_conflict="update"
        )

    def _validate_transactions(
        self, 
        transactions: List[NormalizedTransaction]
    ) -> tuple[List[NormalizedTransaction], int, List[str]]:
        """
        Validate a list of transactions.
        
        Args:
            transactions: List of transactions to validate
            
        Returns:
            Tuple of (valid_transactions, error_count, error_details)
        """
        valid_transactions = []
        error_count = 0
        error_details = []
        
        for i, transaction in enumerate(transactions):
            validation_errors = validate_transaction(transaction)
            
            if not validation_errors:
                valid_transactions.append(transaction)
            else:
                error_count += 1
                error_msg = f"Transaction {i+1} ({transaction.transaction_id}): {', '.join(validation_errors)}"
                error_details.append(error_msg)
                logger.warning(error_msg)
        
        logger.info(f"Validation complete: {len(valid_transactions)} valid, {error_count} invalid")
        return valid_transactions, error_count, error_details

    def get_upload_summary(self, platform: str, days: int = 7) -> Dict[str, Any]:
        """
        Get summary of uploaded transactions for a platform.
        
        Args:
            platform: Platform to summarize
            days: Number of days to include
            
        Returns:
            Summary statistics
        """
        try:
            summary = self.client.get_transaction_summary(platform=platform, days=days)
            return summary
        except Exception as e:
            logger.error(f"Failed to get upload summary: {str(e)}")
            return {"success": False, "error": str(e)}

    def test_connection(self) -> bool:
        """
        Test the Supabase connection.
        
        Returns:
            True if connection works, False otherwise
        """
        return self.client.test_connection()


def upload_strackr_transactions(
    transactions: List[NormalizedTransaction],
    date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    Convenience function to upload Strackr transactions.
    
    Args:
        transactions: List of normalized Strackr transactions
        date: Date for the transactions
        
    Returns:
        Upload results dictionary
    """
    uploader = TransactionUploader()
    return uploader.upload_daily_transactions("strackr", transactions, date)


def upload_shopmy_transactions(
    transactions: List[NormalizedTransaction],
    date: Optional[datetime] = None
) -> Dict[str, Any]:
    """
    Convenience function to upload ShopMy transactions.
    
    Args:
        transactions: List of normalized ShopMy transactions
        date: Date for the transactions
        
    Returns:
        Upload results dictionary
    """
    uploader = TransactionUploader()
    return uploader.upload_daily_transactions("shopmy", transactions, date)


def create_upload_task_function(platform: str):
    """
    Create an Airflow task function for uploading transactions.
    
    Args:
        platform: Platform name ('strackr' or 'shopmy')
        
    Returns:
        Function that can be used as an Airflow task
    """
    def upload_task(**context):
        """
        Airflow task to upload normalized transactions to Supabase.
        """
        # Get normalized transactions from previous task
        ti = context['ti']
        normalized_transactions = ti.xcom_pull(task_ids=f'transform_{platform}_data')
        
        if not normalized_transactions:
            logger.warning(f"No {platform} transactions to upload")
            return {"success": True, "message": "No transactions to upload"}
        
        # Convert dictionaries back to NormalizedTransaction objects if needed
        if normalized_transactions and isinstance(normalized_transactions[0], dict):
            from .schema import NormalizedTransaction
            transactions = []
            for tx_dict in normalized_transactions:
                # Convert ISO strings back to datetime objects
                for date_field in ['transaction_date', 'created_date', 'last_updated']:
                    if tx_dict.get(date_field) and isinstance(tx_dict[date_field], str):
                        tx_dict[date_field] = datetime.fromisoformat(tx_dict[date_field].replace('Z', '+00:00'))
                
                transactions.append(NormalizedTransaction(**tx_dict))
            normalized_transactions = transactions
        
        # Upload transactions
        uploader = TransactionUploader()
        result = uploader.upload_daily_transactions(platform, normalized_transactions)
        
        # Log results
        if result["success"]:
            logger.info(f"Successfully uploaded {result['uploaded_transactions']} {platform} transactions")
        else:
            logger.error(f"Failed to upload {platform} transactions: {result['errors']}")
        
        return result
    
    return upload_task
