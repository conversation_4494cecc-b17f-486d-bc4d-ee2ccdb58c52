"""
Mixpanel API client for fetching analytics data.

This module handles:
- Authentication with Mixpanel Query API
- Fetching user profiles, events, funnels, segmentation, and retention data
- Handling pagination and rate limiting
- Error handling and retries
- Data transformation for Supabase storage
"""

import logging
import requests
import json
import time
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# API Configuration
DEFAULT_PAGE_SIZE = 1000
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
REQUEST_TIMEOUT = 30  # seconds

# Mixpanel Query API Rate Limits
MAX_CONCURRENT_QUERIES = 5
MAX_QUERIES_PER_HOUR = 60
RATE_LIMIT_DELAY = (
    65  # seconds between requests to stay under 60/hour (3600/60 = 60s + buffer)
)


class MixpanelClient:
    """
    Client for interacting with Mixpanel Query API.

    Handles authentication, pagination, and data fetching from all Mixpanel
    query endpoints needed for our analytics requirements.
    """

    def __init__(
        self,
        project_id: str,
        service_account_username: str,
        service_account_secret: str,
        workspace_id: Optional[str] = None,
        respect_rate_limits: bool = True,
    ):
        """
        Initialize Mixpanel client with service account authentication.

        Args:
            project_id: Mixpanel project ID
            service_account_username: Service account username
            service_account_secret: Service account secret
            workspace_id: Optional workspace ID for Data Views
            respect_rate_limits: Whether to enforce rate limiting (60 queries/hour)
        """
        self.project_id = project_id
        self.workspace_id = workspace_id
        self.auth = (service_account_username, service_account_secret)
        self.base_url = "https://mixpanel.com/api/query"
        self.session = requests.Session()
        self.respect_rate_limits = respect_rate_limits

        # Rate limiting tracking
        self.request_count = 0
        self.last_request_time = 0
        self.requests_this_hour = []

        logger.info(f"Initialized Mixpanel client for project: {project_id}")
        if respect_rate_limits:
            logger.info("Rate limiting enabled: max 60 queries/hour")

    def _enforce_rate_limit(self):
        """
        Enforce Mixpanel Query API rate limits:
        - Maximum 60 queries per hour
        - Intelligent spacing to avoid hitting limits
        """
        if not self.respect_rate_limits:
            return

        current_time = time.time()

        # Clean up requests older than 1 hour
        one_hour_ago = current_time - 3600
        self.requests_this_hour = [
            req_time for req_time in self.requests_this_hour if req_time > one_hour_ago
        ]

        # Check if we're approaching the hourly limit
        if len(self.requests_this_hour) >= MAX_QUERIES_PER_HOUR - 5:  # Leave buffer
            oldest_request = min(self.requests_this_hour)
            wait_time = oldest_request + 3600 - current_time
            if wait_time > 0:
                logger.warning(
                    f"Approaching hourly rate limit. Waiting {wait_time:.1f} seconds..."
                )
                time.sleep(wait_time + 5)  # Add small buffer

        # Ensure minimum spacing between requests (60 seconds to be safe)
        if self.last_request_time > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < RATE_LIMIT_DELAY:
                wait_time = RATE_LIMIT_DELAY - time_since_last
                logger.info(
                    f"Rate limiting: waiting {wait_time:.1f} seconds between requests"
                )
                time.sleep(wait_time)

        # Record this request
        self.requests_this_hour.append(current_time)
        self.last_request_time = current_time
        self.request_count += 1

        logger.debug(
            f"Request #{self.request_count}, {len(self.requests_this_hour)} requests in last hour"
        )

    def _make_request(
        self,
        endpoint: str,
        method: str = "GET",
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Make authenticated request to Mixpanel API with rate limiting and retry logic.

        Args:
            endpoint: API endpoint path (without base URL)
            method: HTTP method (GET or POST)
            params: Query parameters for GET requests
            data: Form data for POST requests

        Returns:
            API response data or None if failed
        """
        # Enforce rate limiting before making request
        self._enforce_rate_limit()

        url = f"{self.base_url}/{endpoint}"

        for attempt in range(MAX_RETRIES + 1):
            try:
                logger.debug(
                    f"Making {method} request to {endpoint} (attempt {attempt + 1})"
                )

                if method.upper() == "GET":
                    response = self.session.get(
                        url, auth=self.auth, params=params, timeout=REQUEST_TIMEOUT
                    )
                else:
                    response = self.session.post(
                        url, auth=self.auth, data=data, timeout=REQUEST_TIMEOUT
                    )

                response.raise_for_status()

                result = response.json()
                logger.debug(f"Request successful for {endpoint}")
                return result

            except requests.exceptions.HTTPError as e:
                if response.status_code == 401:
                    logger.error("Authentication failed - check Mixpanel credentials")
                    raise Exception("Mixpanel API authentication failed")
                elif response.status_code == 429:
                    logger.warning("Rate limit exceeded, waiting before retry")
                    time.sleep(RATE_LIMIT_DELAY)  # Wait longer for rate limit
                else:
                    logger.error(f"HTTP error {response.status_code}: {e}")

            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed: {e}")

            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY)

        logger.error(
            f"Failed to make request to {endpoint} after {MAX_RETRIES + 1} attempts"
        )
        return None

    def _add_workspace_id(self, params_or_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add workspace_id to parameters if configured."""
        if self.workspace_id:
            params_or_data["workspace_id"] = self.workspace_id
        return params_or_data

    # ========================================
    # CORE API METHODS (Low-level endpoints)
    # ========================================

    def get_user_profiles(
        self,
        distinct_ids: Optional[List[str]] = None,
        where_filter: Optional[str] = None,
        output_properties: Optional[List[str]] = None,
        page: int = 0,
        session_id: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch user profiles from /engage endpoint.

        Args:
            distinct_ids: List of specific user IDs to query
            where_filter: Filter expression for users
            output_properties: Specific properties to return
            page: Page number for pagination
            session_id: Session ID for pagination

        Returns:
            User profiles response or None if failed
        """
        data = {"project_id": self.project_id}
        data = self._add_workspace_id(data)

        if distinct_ids:
            data["distinct_ids"] = json.dumps(distinct_ids)
        if where_filter:
            data["where"] = where_filter
        if output_properties:
            data["output_properties"] = json.dumps(output_properties)
        if session_id:
            data["session_id"] = session_id
            data["page"] = page

        return self._make_request("engage", method="POST", data=data)

    def get_events_data(
        self,
        events: List[str],
        from_date: str,
        to_date: str,
        event_type: str = "general",
        unit: str = "day",
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch event data from /events endpoint.

        Args:
            events: List of event names to query
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            event_type: "general", "unique", or "average"
            unit: Time granularity ("day", "week", "month")

        Returns:
            Events response or None if failed
        """
        params = {
            "project_id": self.project_id,
            "event": json.dumps(events),
            "type": event_type,
            "unit": unit,
            "from_date": from_date,
            "to_date": to_date,
        }
        params = self._add_workspace_id(params)

        return self._make_request("events", method="GET", params=params)

    def get_segmentation_data(
        self,
        event: str,
        from_date: str,
        to_date: str,
        segment_property: str,
        event_type: str = "unique",
        unit: str = "day",
        limit: int = 100,
        where_filter: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch segmentation data from /segmentation endpoint.

        Args:
            event: Single event name to analyze
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            segment_property: Property to segment by (e.g., "properties.site_domain")
            event_type: "unique" or "general"
            unit: Time granularity
            limit: Number of segments to return
            where_filter: Optional filter expression

        Returns:
            Segmentation response or None if failed
        """
        params = {
            "project_id": self.project_id,
            "event": event,  # Single event, not JSON array
            "from_date": from_date,
            "to_date": to_date,
            "on": segment_property,
            "type": event_type,
            "unit": unit,
            "limit": limit,
        }
        params = self._add_workspace_id(params)

        if where_filter:
            params["where"] = where_filter

        return self._make_request("segmentation", method="GET", params=params)

    def get_funnel_data(
        self,
        funnel_id: int,
        from_date: str,
        to_date: str,
        length: int = 7,
        length_unit: str = "day",
        where_filter: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch funnel data from /funnels endpoint.

        Args:
            funnel_id: ID of saved funnel
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            length: Time window for funnel completion
            length_unit: "day", "hour", "minute", "second"
            where_filter: Optional filter expression

        Returns:
            Funnel response or None if failed
        """
        params = {
            "project_id": self.project_id,
            "funnel_id": funnel_id,
            "from_date": from_date,
            "to_date": to_date,
            "length": length,
            "length_unit": length_unit,
        }
        params = self._add_workspace_id(params)

        if where_filter:
            params["where"] = where_filter

        return self._make_request("funnels", method="GET", params=params)

    def get_cohorts_list(self) -> Optional[List[Dict[str, Any]]]:
        """
        Fetch available cohorts from /cohorts/list endpoint.

        Returns:
            List of cohorts or None if failed
        """
        data = {"project_id": self.project_id}
        data = self._add_workspace_id(data)

        result = self._make_request("cohorts/list", method="POST", data=data)

        # Handle the response format - sometimes it's a list, sometimes wrapped in results
        if result:
            if isinstance(result, list):
                return result
            elif isinstance(result, dict) and "results" in result:
                return result["results"]
            else:
                return result
        return None

    # ========================================
    # BUSINESS METHODS (High-level data requirements)
    # ========================================

    def get_user_data_with_chat(
        self, include_chat_properties: bool = True, user_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get user data + parameters and chat data.

        Args:
            include_chat_properties: Whether to include chat-related properties
            user_filter: Optional filter for specific users

        Returns:
            List of user profiles with chat data
        """
        logger.info("Fetching user data with chat information")

        # Define properties to include
        output_properties = [
            "$email",
            "$first_name",
            "$last_name",
            "$created",
            "$last_seen",
            "subscription_plan",
            "total_purchases",
            "user_segment",
        ]

        if include_chat_properties:
            output_properties.extend(
                [
                    "chat_sessions_count",
                    "last_chat_date",
                    "chat_satisfaction",
                    "support_tickets_count",
                    "chat_response_time_avg",
                ]
            )

        # Get all users with pagination
        all_users = []
        page = 0
        session_id = None

        while True:
            result = self.get_user_profiles(
                where_filter=user_filter,
                output_properties=output_properties,
                page=page,
                session_id=session_id,
            )

            if not result or not result.get("results"):
                break

            all_users.extend(result["results"])

            # Check if we have more pages
            if len(result["results"]) < result.get("page_size", DEFAULT_PAGE_SIZE):
                break

            # Set up for next page
            session_id = result.get("session_id")
            page += 1

        logger.info(f"Retrieved {len(all_users)} user profiles with chat data")
        return all_users

    def get_core_extension_metrics(
        self, from_date: str, to_date: str, unit: str = "day"
    ) -> Dict[str, Any]:
        """
        Get core extension metrics (heartbeat, heartbeat + activity).

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            unit: Time granularity

        Returns:
            Dictionary with core extension metrics
        """
        logger.info(f"Fetching core extension metrics from {from_date} to {to_date}")

        # Define core extension events
        core_events = [
            "heartbeat",
            "heartbeat_with_activity",
            "extension_loaded",
            "extension_active",
            "feature_used",
        ]

        # Get total event counts
        total_events = self.get_events_data(
            events=core_events,
            from_date=from_date,
            to_date=to_date,
            event_type="general",
            unit=unit,
        )

        # Get unique user counts
        unique_users = self.get_events_data(
            events=core_events,
            from_date=from_date,
            to_date=to_date,
            event_type="unique",
            unit=unit,
        )

        return {
            "total_events": total_events,
            "unique_users": unique_users,
            "date_range": {"from": from_date, "to": to_date},
            "unit": unit,
        }

    def get_dau_mau_metrics(
        self, from_date: str, to_date: str, metric_type: str = "DAU"
    ) -> Dict[str, Any]:
        """
        Get Daily Active Users (DAU) or Monthly Active Users (MAU).

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            metric_type: "DAU" or "MAU"

        Returns:
            Dictionary with DAU/MAU metrics
        """
        logger.info(f"Fetching {metric_type} metrics from {from_date} to {to_date}")

        # Define what constitutes an "active" user
        # Added phia_clicked as it represents active engagement with the extension
        active_events = [
            "extension_loaded",
            "heartbeat",
            "feature_used",
            "content_interaction",
            "user_action",
            "phia_clicked",
        ]

        # Define what constitutes an "installed" user
        installed_events = ["extension_loaded", "heartbeat"]

        unit = "day" if metric_type == "DAU" else "month"

        # Get active users
        active_users = self.get_events_data(
            events=active_events,
            from_date=from_date,
            to_date=to_date,
            event_type="unique",
            unit=unit,
        )

        # Get installed users
        installed_users = self.get_events_data(
            events=installed_events,
            from_date=from_date,
            to_date=to_date,
            event_type="unique",
            unit=unit,
        )

        return {
            "active_users": active_users,
            "installed_users": installed_users,
            "metric_type": metric_type,
            "date_range": {"from": from_date, "to": to_date},
        }

    def get_onboarding_conversion(
        self, from_date: str, to_date: str, funnel_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get onboarding conversion (first time run) metrics.

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            funnel_id: Optional saved funnel ID

        Returns:
            Dictionary with onboarding conversion data
        """
        logger.info(f"Fetching onboarding conversion from {from_date} to {to_date}")

        if funnel_id:
            # Use saved funnel
            funnel_data = self.get_funnel_data(
                funnel_id=funnel_id,
                from_date=from_date,
                to_date=to_date,
                length=7,  # 7 days to complete onboarding
                length_unit="day",
            )
            return {
                "funnel_data": funnel_data,
                "date_range": {"from": from_date, "to": to_date},
            }
        else:
            # Calculate manually using events
            onboarding_steps = [
                "extension_installed",
                "first_launch",
                "tutorial_started",
                "tutorial_completed",
                "first_feature_used",
            ]

            results = {}

            for event in onboarding_steps:
                event_data = self.get_events_data(
                    events=[event],
                    from_date=from_date,
                    to_date=to_date,
                    event_type="unique",
                    unit="day",
                )
                results[event] = event_data

            return {
                "onboarding_steps": results,
                "date_range": {"from": from_date, "to": to_date},
            }

    def get_activation_rate_per_site(
        self,
        from_date: str,
        to_date: str,
        activation_event: str = "feature_activated",
        limit: int = 100,
    ) -> Dict[str, Any]:
        """
        Get activation rates broken down by site/domain.

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            activation_event: Event that represents activation
            limit: Number of top sites to return

        Returns:
            Dictionary with activation rates per site
        """
        logger.info(f"Fetching activation rates per site from {from_date} to {to_date}")

        # Get total users per site (any activity)
        total_users = self.get_segmentation_data(
            event="extension_used",  # General usage event
            from_date=from_date,
            to_date=to_date,
            segment_property="properties.site_domain",
            event_type="unique",
            unit="day",
            limit=limit,
        )

        # Get activated users per site
        activated_users = self.get_segmentation_data(
            event=activation_event,
            from_date=from_date,
            to_date=to_date,
            segment_property="properties.site_domain",
            event_type="unique",
            unit="day",
            limit=limit,
        )

        return {
            "total_users_per_site": total_users,
            "activated_users_per_site": activated_users,
            "activation_event": activation_event,
            "date_range": {"from": from_date, "to": to_date},
        }

    def get_weekly_retention_by_cohort(
        self,
        cohort_start_date: str,
        analysis_weeks: int = 8,
        cohort_id: Optional[int] = None,
        cohort_filter: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get weekly retention rates filtered by cohort.

        Args:
            cohort_start_date: Start date of the cohort (YYYY-MM-DD)
            analysis_weeks: Number of weeks to analyze retention
            cohort_id: Optional specific cohort ID
            cohort_filter: Optional filter for custom cohort

        Returns:
            Dictionary with weekly retention data
        """
        logger.info(
            f"Fetching weekly retention for cohort starting {cohort_start_date}"
        )

        # Get cohort users
        if cohort_id:
            # Use specific cohort
            cohort_users = self.get_user_profiles(
                where_filter=f'filter_by_cohort({{"id": {cohort_id}}})',
                output_properties=["$created_at", "$last_seen"],
            )
        else:
            # Use custom filter or date-based cohort
            where_filter = (
                cohort_filter or f'properties["$created_at"] >= "{cohort_start_date}"'
            )
            cohort_users = self.get_user_profiles(
                where_filter=where_filter,
                output_properties=["$created_at", "$last_seen"],
            )

        if not cohort_users or not cohort_users.get("results"):
            logger.warning("No cohort users found")
            return {
                "cohort_size": 0,
                "weekly_retention": {},
                "cohort_start_date": cohort_start_date,
            }

        user_ids = [user["$distinct_id"] for user in cohort_users["results"]]
        total_cohort_size = len(user_ids)

        # Calculate retention for each week
        retention_data = {
            "cohort_size": total_cohort_size,
            "cohort_start_date": cohort_start_date,
            "weekly_retention": {},
        }

        # For each week, check how many users were active
        from datetime import datetime, timedelta

        start_date = datetime.strptime(cohort_start_date, "%Y-%m-%d")

        for week in range(1, analysis_weeks + 1):
            week_start = start_date + timedelta(weeks=week)
            week_end = week_start + timedelta(days=6)

            # Get users active in this week
            week_filter = f'properties["$last_seen"] >= "{week_start.strftime("%Y-%m-%d")}" and properties["$last_seen"] <= "{week_end.strftime("%Y-%m-%d")}"'

            active_users = self.get_user_profiles(
                distinct_ids=user_ids,
                where_filter=week_filter,
                output_properties=["$last_seen"],
            )

            active_count = len(active_users.get("results", [])) if active_users else 0
            retention_rate = (
                (active_count / total_cohort_size * 100) if total_cohort_size > 0 else 0
            )

            retention_data["weekly_retention"][f"week_{week}"] = {
                "active_users": active_count,
                "retention_rate": round(retention_rate, 2),
                "week_start": week_start.strftime("%Y-%m-%d"),
                "week_end": week_end.strftime("%Y-%m-%d"),
            }

        return retention_data

    # ========================================
    # COMPREHENSIVE DATA COLLECTION
    # ========================================

    def get_all_analytics_data(
        self,
        from_date: str,
        to_date: str,
        include_user_data: bool = True,
        include_retention: bool = True,
        cohort_id: Optional[int] = None,
        funnel_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Get comprehensive analytics data for all requirements.

        This method fetches all the data needed for Supabase:
        - User data + params and chat data
        - Core extension metrics (heartbeat, heartbeat + activity)
        - DAU, MAU (active or installed)
        - Onboarding conversion (first time run)
        - Activation rate per site
        - Weekly retention + filter by cohort

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            include_user_data: Whether to fetch user profiles (can be slow)
            include_retention: Whether to fetch retention data (can be slow)
            cohort_id: Optional cohort ID for retention analysis
            funnel_id: Optional funnel ID for onboarding analysis

        Returns:
            Dictionary with all analytics data
        """
        logger.info(
            f"Fetching comprehensive analytics data from {from_date} to {to_date}"
        )

        results = {
            "date_range": {"from": from_date, "to": to_date},
            "timestamp": datetime.now().isoformat(),
            "data": {},
        }

        try:
            # 1. User data + chat data
            if include_user_data:
                logger.info("Fetching user data with chat information...")
                results["data"]["user_data"] = self.get_user_data_with_chat()

            # 2. Core extension metrics
            logger.info("Fetching core extension metrics...")
            results["data"]["core_metrics"] = self.get_core_extension_metrics(
                from_date, to_date
            )

            # 3. DAU metrics
            logger.info("Fetching DAU metrics...")
            results["data"]["dau"] = self.get_dau_mau_metrics(from_date, to_date, "DAU")

            # 4. MAU metrics
            logger.info("Fetching MAU metrics...")
            results["data"]["mau"] = self.get_dau_mau_metrics(from_date, to_date, "MAU")

            # 5. Onboarding conversion
            logger.info("Fetching onboarding conversion...")
            results["data"]["onboarding"] = self.get_onboarding_conversion(
                from_date, to_date, funnel_id
            )

            # 6. Activation rate per site
            logger.info("Fetching activation rates per site...")
            results["data"]["site_activation"] = self.get_activation_rate_per_site(
                from_date, to_date
            )

            # 7. Weekly retention (optional, can be slow)
            if include_retention:
                logger.info("Fetching weekly retention data...")
                results["data"]["retention"] = self.get_weekly_retention_by_cohort(
                    cohort_start_date=from_date, analysis_weeks=8, cohort_id=cohort_id
                )

            logger.info("Successfully fetched all analytics data")
            results["success"] = True

        except Exception as e:
            logger.error(f"Error fetching analytics data: {str(e)}")
            results["success"] = False
            results["error"] = str(e)

        return results

    def get_safari_extension_metrics(
        self, from_date: str, to_date: str, unit: str = "day"
    ) -> Dict[str, Any]:
        """
        Get Safari extension specific metrics filtered by platform.

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            unit: Time granularity

        Returns:
            Dictionary with Safari extension metrics
        """
        logger.info(f"Fetching Safari extension metrics from {from_date} to {to_date}")

        # Safari extension events
        safari_events = ["phia_clicked", "phia_shown", "heartbeat"]

        results = {}

        # Get platform segmentation for each event
        for event in safari_events:
            try:
                platform_data = self.get_segmentation_data(
                    event=event,
                    segment_property="properties[platform]",
                    from_date=from_date,
                    to_date=to_date,
                    event_type="general",
                    unit=unit,
                )

                # Extract Safari extension data
                safari_data = {}
                if platform_data and "data" in platform_data:
                    for date, segments in platform_data["data"].items():
                        if segments and "IOS_SAFARI_EXTENSION" in segments:
                            safari_data[date] = segments["IOS_SAFARI_EXTENSION"]
                        else:
                            safari_data[date] = 0

                results[f"{event}_safari"] = safari_data

            except Exception as e:
                logger.warning(f"Failed to get Safari data for {event}: {e}")
                results[f"{event}_safari"] = {}

        return results

    def get_safari_extension_users(
        self, from_date: str, to_date: str, unit: str = "day"
    ) -> Dict[str, Any]:
        """
        Get unique Safari extension users (distinct count of phia_id).

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            unit: Time granularity

        Returns:
            Dictionary with Safari extension user counts
        """
        logger.info(f"Fetching Safari extension users from {from_date} to {to_date}")

        try:
            # Get unique users for heartbeat event filtered by Safari platform
            platform_data = self.get_segmentation_data(
                event="heartbeat",
                segment_property="properties.platform",
                from_date=from_date,
                to_date=to_date,
                event_type="unique",  # Unique users
                unit=unit,
            )

            # Extract Safari extension users
            safari_users = {}
            if platform_data and "data" in platform_data:
                for date, segments in platform_data["data"].items():
                    if segments and "IOS_SAFARI_EXTENSION" in segments:
                        safari_users[date] = segments["IOS_SAFARI_EXTENSION"]
                    else:
                        safari_users[date] = 0

            return {
                "safari_extension_users": safari_users,
                "date_range": {"from": from_date, "to": to_date},
                "unit": unit,
            }

        except Exception as e:
            logger.error(f"Failed to get Safari extension users: {e}")
            return {
                "safari_extension_users": {},
                "error": str(e),
            }

    def get_site_activation_rates(
        self, from_date: str, to_date: str, unit: str = "day", limit: int = 50
    ) -> Dict[str, Any]:
        """
        Calculate site activation rates: (phia_clicked/phia_shown)*100 by hostname.

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            unit: Time granularity
            limit: Number of top sites to return

        Returns:
            Dictionary with activation rates per site
        """
        logger.info(f"Calculating site activation rates from {from_date} to {to_date}")

        try:
            # Get phia_shown events by hostname (remove where filter for now)
            shown_data = self.get_segmentation_data(
                event="phia_shown",
                segment_property="properties.hostname",
                from_date=from_date,
                to_date=to_date,
                event_type="general",
                unit=unit,
                limit=limit,
            )

            # Get phia_clicked events by hostname (remove where filter for now)
            clicked_data = self.get_segmentation_data(
                event="phia_clicked",
                segment_property="properties.hostname",
                from_date=from_date,
                to_date=to_date,
                event_type="general",
                unit=unit,
                limit=limit,
            )

            # Calculate activation rates
            activation_rates = {}
            site_totals = {}

            # Aggregate data across all dates
            if shown_data and "data" in shown_data:
                for date, segments in shown_data["data"].items():
                    if segments:
                        for hostname, count in segments.items():
                            if hostname not in site_totals:
                                site_totals[hostname] = {"shown": 0, "clicked": 0}
                            site_totals[hostname]["shown"] += count

            if clicked_data and "data" in clicked_data:
                for date, segments in clicked_data["data"].items():
                    if segments:
                        for hostname, count in segments.items():
                            if hostname not in site_totals:
                                site_totals[hostname] = {"shown": 0, "clicked": 0}
                            site_totals[hostname]["clicked"] += count

            # Calculate activation rates
            for hostname, totals in site_totals.items():
                shown = totals["shown"]
                clicked = totals["clicked"]

                if shown > 0:
                    activation_rate = (clicked / shown) * 100
                    activation_rates[hostname] = {
                        "shown": shown,
                        "clicked": clicked,
                        "activation_rate": round(activation_rate, 2),
                    }

            # Sort by activation rate descending
            sorted_rates = dict(
                sorted(
                    activation_rates.items(),
                    key=lambda x: x[1]["activation_rate"],
                    reverse=True,
                )
            )

            return {
                "site_activation_rates": sorted_rates,
                "date_range": {"from": from_date, "to": to_date},
                "total_sites": len(sorted_rates),
            }

        except Exception as e:
            logger.error(f"Failed to calculate site activation rates: {e}")
            return {
                "site_activation_rates": {},
                "error": str(e),
            }

    def get_onboarding_funnel_analysis(
        self, from_date: str, to_date: str, unit: str = "day"
    ) -> Dict[str, Any]:
        """
        Analyze onboarding funnel: Enabled Permissions % First-Time Run.

        Funnel steps:
        1. page_view with pathname="/mobile/almost-finished" (First time ever)
        2. page_view with domain="phia.com" (Completed onboarding)

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            unit: Time granularity

        Returns:
            Dictionary with funnel analysis
        """
        logger.info(f"Analyzing onboarding funnel from {from_date} to {to_date}")

        try:
            # Step 1: Get users who reached /mobile/almost-finished
            step1_data = self.get_segmentation_data(
                event="page_view",
                segment_property="properties.pathname",
                from_date=from_date,
                to_date=to_date,
                event_type="unique",  # Unique users
                unit=unit,
            )

            # Step 2: Get users who completed onboarding (reached phia.com)
            step2_data = self.get_segmentation_data(
                event="page_view",
                segment_property="properties.domain",
                from_date=from_date,
                to_date=to_date,
                event_type="unique",  # Unique users
                unit=unit,
            )

            # Calculate funnel metrics
            funnel_results = {}

            # Extract step 1 counts (almost-finished page)
            step1_counts = {}
            if step1_data and "data" in step1_data:
                for date, segments in step1_data["data"].items():
                    if segments and "/mobile/almost-finished" in segments:
                        step1_counts[date] = segments["/mobile/almost-finished"]
                    else:
                        step1_counts[date] = 0

            # Extract step 2 counts (phia.com domain)
            step2_counts = {}
            if step2_data and "data" in step2_data:
                for date, segments in step2_data["data"].items():
                    if segments and "phia.com" in segments:
                        step2_counts[date] = segments["phia.com"]
                    else:
                        step2_counts[date] = 0

            # Calculate conversion rates by date
            conversion_rates = {}
            total_step1 = 0
            total_step2 = 0

            all_dates = set(step1_counts.keys()) | set(step2_counts.keys())

            for date in all_dates:
                step1 = step1_counts.get(date, 0)
                step2 = step2_counts.get(date, 0)

                total_step1 += step1
                total_step2 += step2

                if step1 > 0:
                    conversion_rate = (step2 / step1) * 100
                else:
                    conversion_rate = 0

                conversion_rates[date] = {
                    "step1_users": step1,
                    "step2_users": step2,
                    "conversion_rate": round(conversion_rate, 2),
                }

            # Calculate overall conversion rate
            overall_conversion = 0
            if total_step1 > 0:
                overall_conversion = (total_step2 / total_step1) * 100

            return {
                "funnel_analysis": {
                    "step1_total": total_step1,
                    "step2_total": total_step2,
                    "overall_conversion_rate": round(overall_conversion, 2),
                    "daily_breakdown": conversion_rates,
                },
                "date_range": {"from": from_date, "to": to_date},
                "funnel_steps": [
                    "page_view: pathname=/mobile/almost-finished",
                    "page_view: domain=phia.com",
                ],
            }

        except Exception as e:
            logger.error(f"Failed to analyze onboarding funnel: {e}")
            return {
                "funnel_analysis": {},
                "error": str(e),
            }

    def get_weekly_retention_cohort_analysis(
        self, cohort_start_date: str, analysis_weeks: int = 9, unit: str = "week"
    ) -> Dict[str, Any]:
        """
        Analyze weekly retention for users who activated permissions.

        This tracks users from a specific cohort (e.g., Jun 19-25, 2025) and
        measures their retention over multiple weeks using heartbeat events.

        Args:
            cohort_start_date: Start date for cohort (YYYY-MM-DD)
            analysis_weeks: Number of weeks to analyze retention
            unit: Time granularity (should be "week")

        Returns:
            Dictionary with weekly retention analysis
        """
        logger.info(
            f"Analyzing weekly retention for cohort starting {cohort_start_date}"
        )

        try:
            from datetime import datetime, timedelta

            # Calculate cohort end date (1 week later)
            start_dt = datetime.strptime(cohort_start_date, "%Y-%m-%d")
            cohort_end_date = (start_dt + timedelta(days=6)).strftime("%Y-%m-%d")

            # Get users who activated permissions in the cohort period
            # Using phia_clicked as the activation event
            cohort_users_data = self.get_segmentation_data(
                event="phia_clicked",
                segment_property="properties.phia_id",
                from_date=cohort_start_date,
                to_date=cohort_end_date,
                event_type="unique",
                unit="day",
                limit=10000,  # Get all users
            )

            # Extract unique user IDs from cohort
            cohort_user_ids = set()
            if cohort_users_data and "data" in cohort_users_data:
                for date, segments in cohort_users_data["data"].items():
                    if segments:
                        cohort_user_ids.update(segments.keys())

            cohort_size = len(cohort_user_ids)
            logger.info(f"Cohort size: {cohort_size} users")

            if cohort_size == 0:
                return {
                    "cohort_analysis": {
                        "cohort_size": 0,
                        "weekly_retention": {},
                        "retention_curve": [],
                    },
                    "cohort_period": {
                        "start": cohort_start_date,
                        "end": cohort_end_date,
                    },
                    "error": "No users found in cohort period",
                }

            # Analyze retention for each week
            weekly_retention = {}
            retention_curve = []

            for week in range(analysis_weeks):
                # Calculate week start/end dates
                week_start = start_dt + timedelta(weeks=week)
                week_end = week_start + timedelta(days=6)

                week_start_str = week_start.strftime("%Y-%m-%d")
                week_end_str = week_end.strftime("%Y-%m-%d")

                # Get active users for this week (using heartbeat events)
                week_activity = self.get_segmentation_data(
                    event="heartbeat",
                    segment_property="properties.phia_id",
                    from_date=week_start_str,
                    to_date=week_end_str,
                    event_type="unique",
                    unit="day",
                    limit=10000,
                )

                # Count how many cohort users were active this week
                active_cohort_users = set()
                if week_activity and "data" in week_activity:
                    for date, segments in week_activity["data"].items():
                        if segments:
                            active_cohort_users.update(segments.keys())

                # Calculate retention
                retained_users = cohort_user_ids.intersection(active_cohort_users)
                retention_count = len(retained_users)
                retention_rate = (
                    (retention_count / cohort_size) * 100 if cohort_size > 0 else 0
                )

                weekly_retention[f"week_{week}"] = {
                    "week_start": week_start_str,
                    "week_end": week_end_str,
                    "retained_users": retention_count,
                    "retention_rate": round(retention_rate, 2),
                }

                retention_curve.append(
                    {
                        "week": week,
                        "retention_rate": round(retention_rate, 2),
                    }
                )

                logger.debug(
                    f"Week {week}: {retention_count}/{cohort_size} users retained ({retention_rate:.1f}%)"
                )

            return {
                "cohort_analysis": {
                    "cohort_size": cohort_size,
                    "weekly_retention": weekly_retention,
                    "retention_curve": retention_curve,
                },
                "cohort_period": {"start": cohort_start_date, "end": cohort_end_date},
                "analysis_weeks": analysis_weeks,
            }

        except Exception as e:
            logger.error(f"Failed to analyze weekly retention: {e}")
            return {
                "cohort_analysis": {},
                "error": str(e),
            }

    def get_all_analytics_data_optimized(
        self,
        from_date: str,
        to_date: str,
        include_user_data: bool = False,  # Default to False to save API calls
        include_retention: bool = False,  # Default to False to save API calls
    ) -> Dict[str, Any]:
        """
        Get comprehensive analytics data optimized for rate limits.

        This method minimizes API calls by:
        1. Using real events that exist in your data
        2. Combining multiple metrics in single requests
        3. Skipping expensive operations by default
        4. Prioritizing most important data first

        Args:
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            include_user_data: Whether to fetch user profiles (uses 1+ API calls)
            include_retention: Whether to fetch retention data (uses many API calls)

        Returns:
            Dictionary with all analytics data
        """
        logger.info(f"Fetching optimized analytics data from {from_date} to {to_date}")
        logger.info(f"Rate limiting enabled: {self.respect_rate_limits}")

        results = {
            "date_range": {"from": from_date, "to": to_date},
            "timestamp": datetime.now().isoformat(),
            "data": {},
            "api_calls_used": 0,
        }

        try:
            # Real events that exist in your Mixpanel data
            # Added phia_clicked and phia_shown for new dashboard components
            core_events = [
                "extension_page_view",
                "heartbeat",
                "page_view",
                "phia_clicked",
                "phia_shown",
            ]

            # 1. Core metrics - Total events (1 API call)
            logger.info("Fetching total events for core metrics...")
            total_events = self.get_events_data(
                events=core_events,
                from_date=from_date,
                to_date=to_date,
                event_type="general",
                unit="day",
            )
            results["data"]["total_events"] = total_events
            results["api_calls_used"] += 1

            # 2. Unique users - DAU/MAU (1 API call)
            logger.info("Fetching unique users (DAU)...")
            unique_users = self.get_events_data(
                events=core_events,
                from_date=from_date,
                to_date=to_date,
                event_type="unique",
                unit="day",
            )
            results["data"]["unique_users"] = unique_users
            results["api_calls_used"] += 1

            # 3. User profiles sample (1 API call) - only if requested
            if include_user_data:
                logger.info("Fetching user profiles sample...")
                try:
                    user_profiles = self.get_user_profiles(
                        output_properties=[
                            "$email",
                            "$created_at",
                            "$last_seen",
                            "$onboarded_at",
                        ],
                        page=0,
                    )
                    results["data"]["user_profiles"] = {
                        "sample": (
                            user_profiles.get("results", [])[:100]
                            if user_profiles
                            else []
                        ),
                        "total_count": (
                            user_profiles.get("total", 0) if user_profiles else 0
                        ),
                    }
                    results["api_calls_used"] += 1
                except Exception as e:
                    logger.warning(f"User profiles failed: {e}")
                    results["data"]["user_profiles"] = None

            # NEW DASHBOARD COMPONENTS - Add the 5 missing components
            logger.info("Fetching new dashboard components...")

            # 4. Safari Extension Metrics (1 API call)
            try:
                safari_metrics = self.get_safari_extension_users(
                    from_date=from_date,
                    to_date=to_date,
                    unit="day",
                )
                results["data"]["safari_extension_metrics"] = safari_metrics
                results["api_calls_used"] += 1
            except Exception as e:
                logger.warning(f"Safari extension metrics failed: {e}")
                results["data"]["safari_extension_metrics"] = None

            # 5. Site Activation Rates (2 API calls)
            try:
                site_activation = self.get_site_activation_rates(
                    from_date=from_date,
                    to_date=to_date,
                    unit="day",
                    limit=50,
                )
                results["data"]["site_activation_rates"] = site_activation
                results["api_calls_used"] += 2  # phia_shown + phia_clicked segmentation
            except Exception as e:
                logger.warning(f"Site activation rates failed: {e}")
                results["data"]["site_activation_rates"] = None

            # 6. Onboarding Funnel Analysis (2 API calls)
            try:
                onboarding_funnel = self.get_onboarding_funnel_analysis(
                    from_date=from_date,
                    to_date=to_date,
                    unit="day",
                )
                results["data"]["onboarding_funnel"] = onboarding_funnel
                results["api_calls_used"] += 2  # pathname + domain segmentation
            except Exception as e:
                logger.warning(f"Onboarding funnel analysis failed: {e}")
                results["data"]["onboarding_funnel"] = None

            # Calculate derived metrics from the data we have
            results["data"]["derived_metrics"] = self._calculate_derived_metrics(
                total_events, unique_users, from_date, to_date
            )

            logger.info(
                f"Successfully fetched analytics data using {results['api_calls_used']} API calls"
            )
            results["success"] = True

        except Exception as e:
            logger.error(f"Error fetching analytics data: {str(e)}")
            results["success"] = False
            results["error"] = str(e)

        return results

    def _calculate_derived_metrics(
        self,
        total_events: Optional[Dict],
        unique_users: Optional[Dict],
        from_date: str,
        to_date: str,
    ) -> Dict[str, Any]:
        """Calculate derived metrics from raw event data."""
        metrics = {}

        if total_events and total_events.get("data", {}).get("values"):
            values = total_events["data"]["values"]

            # Calculate totals for each event
            for event, daily_data in values.items():
                total = sum(daily_data.values()) if daily_data else 0
                metrics[f"{event}_total"] = total

        if unique_users and unique_users.get("data", {}).get("values"):
            values = unique_users["data"]["values"]

            # Calculate DAU for each event
            for event, daily_data in values.items():
                total_unique = sum(daily_data.values()) if daily_data else 0
                metrics[f"{event}_dau"] = total_unique

        # Calculate engagement rate (events per user)
        if (
            "extension_page_view_total" in metrics
            and "extension_page_view_dau" in metrics
        ):
            if metrics["extension_page_view_dau"] > 0:
                metrics["engagement_rate"] = round(
                    metrics["extension_page_view_total"]
                    / metrics["extension_page_view_dau"],
                    2,
                )

        return metrics

    def test_connection(self) -> bool:
        """
        Test the Mixpanel API connection.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Try a simple request to test connection
            result = self.get_user_profiles(output_properties=["$email"], page=0)

            if result is not None:
                logger.info("Mixpanel connection test successful")
                return True
            else:
                logger.error("Mixpanel connection test failed")
                return False

        except Exception as e:
            logger.error(f"Mixpanel connection test failed: {str(e)}")
            return False
