"""
Airflow task functions for Mixpanel data pipeline.

This module contains the main task functions that will be called by the DAG:
- fetch_mixpanel_analytics_task: Fetch all analytics data from Mixpanel API
- transform_mixpanel_data_task: Transform raw data to normalized schemas
- prepare_supabase_data_task: Prepare data for Supabase upload
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

from .mixpanel_client import MixpanelClient
from .mixpanel_transform import (
    transform_user_profiles,
    transform_daily_metrics,
    transform_site_activation,
    transform_browser_segmentation,
    transform_site_activation_rates,
    transform_onboarding_funnel,
    validate_transformed_data,
)

logger = logging.getLogger(__name__)


def fetch_mixpanel_analytics_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to fetch comprehensive analytics data from Mixpanel API.

    This task uses the optimized client to fetch all required data with minimal API calls.

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with all raw analytics data
    """
    logger.info("Starting Mixpanel analytics fetch task")

    # Get execution date and target date
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    logger.info(f"Fetching Mixpanel analytics for date: {date_str}")

    try:
        # Initialize Mixpanel client with credentials from environment
        client = MixpanelClient(
            project_id=os.getenv("MIXPANEL_PROJECT_ID"),
            service_account_username=os.getenv("MIXPANEL_SERVICE_USERNAME"),
            service_account_secret=os.getenv("MIXPANEL_SERVICE_SECRET"),
            workspace_id=os.getenv("MIXPANEL_WORKSPACE_ID"),
            respect_rate_limits=True,  # Always respect rate limits in production
        )

        # Use optimized method to fetch all data (2-3 API calls total)
        analytics_data = client.get_all_analytics_data_optimized(
            from_date=date_str,
            to_date=date_str,
            include_user_data=True,  # Include user profiles
            include_retention=False,  # Skip retention for now to save API calls
        )

        if analytics_data and analytics_data.get("success"):
            logger.info(
                f"Successfully fetched analytics data using {analytics_data.get('api_calls_used', 0)} API calls"
            )

            # Log key metrics
            derived = analytics_data.get("data", {}).get("derived_metrics", {})
            if derived:
                logger.info(f"Key metrics: {derived}")

            return analytics_data
        else:
            error_msg = (
                analytics_data.get("error", "Unknown error")
                if analytics_data
                else "No data returned"
            )
            logger.error(f"Failed to fetch analytics data: {error_msg}")
            raise Exception(f"Mixpanel fetch failed: {error_msg}")

    except Exception as e:
        logger.error(f"Error in fetch task: {str(e)}")
        raise


def transform_mixpanel_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to transform raw Mixpanel data to normalized schemas.

    This task:
    - Gets raw analytics data from previous task via XCom
    - Transforms data to all required Supabase schemas
    - Validates data quality
    - Returns transformed data for each table

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with transformed data for each Supabase table
    """
    logger.info("Starting Mixpanel data transformation task")

    # Get raw analytics data from previous task
    ti = kwargs["ti"]
    analytics_data = ti.xcom_pull(task_ids="fetch_mixpanel_analytics")

    if not analytics_data or not analytics_data.get("success"):
        raise Exception("No valid analytics data received from fetch task")

    # Get target date
    execution_date = kwargs.get("execution_date", datetime.now())
    target_date = execution_date - timedelta(days=1)
    date_str = target_date.strftime("%Y-%m-%d")

    logger.info(f"Transforming analytics data for {date_str}")

    raw_data = analytics_data.get("data", {})
    transformed_data = {}
    validation_results = {}

    try:
        # 1. Transform user profiles (if available)
        if raw_data.get("user_profiles") and raw_data["user_profiles"].get("sample"):
            logger.info("Transforming user profiles...")
            user_profiles = transform_user_profiles(raw_data["user_profiles"]["sample"])
            transformed_data["user_profiles"] = user_profiles
            validation_results["user_profiles"] = validate_transformed_data(
                user_profiles, "user_profiles"
            )

        # 2. Transform daily metrics
        if raw_data.get("total_events") and raw_data.get("unique_users"):
            logger.info("Transforming daily metrics...")
            daily_metrics = transform_daily_metrics(
                raw_data["total_events"], raw_data["unique_users"], date_str
            )
            transformed_data["daily_metrics"] = daily_metrics
            validation_results["daily_metrics"] = validate_transformed_data(
                daily_metrics, "daily_metrics"
            )

        # 3. Transform browser segmentation (if available)
        if raw_data.get("browser_segmentation"):
            logger.info("Transforming browser segmentation...")
            browser_segmentation = transform_browser_segmentation(
                raw_data["browser_segmentation"], date_str, "browser"
            )
            transformed_data["browser_segmentation"] = browser_segmentation
            validation_results["browser_segmentation"] = validate_transformed_data(
                browser_segmentation, "browser_segmentation"
            )

        # 4. Transform site activation rates (NEW)
        if raw_data.get("site_activation_rates"):
            logger.info("Transforming site activation rates...")
            site_activation_rates = transform_site_activation_rates(
                raw_data["site_activation_rates"], date_str
            )
            transformed_data["site_activation"] = site_activation_rates
            validation_results["site_activation"] = validate_transformed_data(
                site_activation_rates, "site_activation"
            )

        # 5. Transform onboarding funnel (NEW)
        if raw_data.get("onboarding_funnel"):
            logger.info("Transforming onboarding funnel...")
            onboarding_funnel = transform_onboarding_funnel(
                raw_data["onboarding_funnel"], date_str
            )
            transformed_data["onboarding_funnel"] = onboarding_funnel
            validation_results["onboarding_funnel"] = validate_transformed_data(
                onboarding_funnel, "onboarding_funnel"
            )

        # 6. Transform Safari extension metrics (NEW)
        if raw_data.get("safari_extension_metrics"):
            logger.info("Transforming Safari extension metrics...")
            # Use existing daily_metrics transform but with Safari-specific data
            safari_metrics_data = raw_data["safari_extension_metrics"]
            if safari_metrics_data.get("safari_extension_users"):
                # Create a simple record for Safari users
                safari_records = [
                    {
                        "date": date_str,
                        "event_name": "safari_extension_users",
                        "total_events": 0,
                        "unique_users": safari_metrics_data[
                            "safari_extension_users"
                        ].get(date_str, 0),
                        "metric_type": "safari_extension",
                    }
                ]
                transformed_data["safari_extension_metrics"] = safari_records
                validation_results["safari_extension_metrics"] = (
                    validate_transformed_data(
                        safari_records, "safari_extension_metrics"
                    )
                )

        # Calculate overall statistics
        total_records = sum(len(data) for data in transformed_data.values())
        total_valid = sum(
            result.get("valid_records", 0) for result in validation_results.values()
        )

        results = {
            "success": True,
            "target_date": date_str,
            "transformed_data": transformed_data,
            "validation_results": validation_results,
            "statistics": {
                "total_records": total_records,
                "total_valid_records": total_valid,
                "tables_created": len(transformed_data),
                "processing_time": datetime.now().isoformat(),
            },
        }

        logger.info(
            f"Transformation complete: {total_records} records across {len(transformed_data)} tables"
        )
        return results

    except Exception as e:
        logger.error(f"Error in transform task: {str(e)}")
        raise


def prepare_supabase_data_task(**kwargs) -> Dict[str, Any]:
    """
    Airflow task to prepare transformed data for Supabase upload.

    This task:
    - Gets transformed data from previous task
    - Formats data for Supabase batch upload
    - Performs final validation
    - Returns data ready for Supabase upload

    Args:
        **kwargs: Airflow context variables

    Returns:
        Dictionary with data prepared for Supabase upload
    """
    logger.info("Starting Supabase data preparation task")

    # Get transformed data from previous task
    ti = kwargs["ti"]
    transform_results = ti.xcom_pull(task_ids="transform_mixpanel_data")

    if not transform_results or not transform_results.get("success"):
        raise Exception("No valid transformed data received from transform task")

    transformed_data = transform_results.get("transformed_data", {})
    target_date = transform_results.get("target_date")

    logger.info(f"Preparing {len(transformed_data)} tables for Supabase upload")

    # Prepare data for each table
    supabase_ready_data = {}
    upload_statistics = {}

    for table_name, records in transformed_data.items():
        if records:
            # Filter out any records with missing required fields
            valid_records = []
            for record in records:
                # Remove any None values that would cause Supabase issues
                cleaned_record = {k: v for k, v in record.items() if v is not None}
                if cleaned_record:
                    valid_records.append(cleaned_record)

            supabase_ready_data[table_name] = valid_records
            upload_statistics[table_name] = {
                "total_records": len(records),
                "valid_records": len(valid_records),
                "ready_for_upload": len(valid_records) > 0,
            }

            logger.info(
                f"{table_name}: {len(valid_records)}/{len(records)} records ready for upload"
            )

    results = {
        "success": True,
        "target_date": target_date,
        "supabase_data": supabase_ready_data,
        "upload_statistics": upload_statistics,
        "total_tables": len(supabase_ready_data),
        "total_records": sum(len(data) for data in supabase_ready_data.values()),
        "prepared_at": datetime.now().isoformat(),
    }

    logger.info(
        f"Supabase preparation complete: {results['total_records']} records across {results['total_tables']} tables"
    )
    return results
