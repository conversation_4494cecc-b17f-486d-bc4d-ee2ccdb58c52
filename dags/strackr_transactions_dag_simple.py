"""
Simplified Strackr Transaction Reports DAG

This DAG fetches transactions from Strackr API, transforms them to a normalized schema,
and stores them in Supabase.

Schedule: Daily at 2 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
import logging

# Import our simplified functions
from dependencies.strackr import (
    fetch_strackr_transactions,
    transform_strackr_transactions,
    upload_to_supabase
)

logger = logging.getLogger(__name__)

# DAG Configuration
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "start_date": datetime(2024, 1, 1),
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
    "email": ["<EMAIL>"],
}

# Create the DAG
dag = DAG(
    "strackr_transactions_simple",
    default_args=default_args,
    description="Simplified Strackr transaction processing",
    schedule_interval="0 2 * * *",  # Daily at 2 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["transaction", "strackr", "simple"],
    max_active_runs=1,
)

# Task 1: Fetch transactions from Strackr API
fetch_task = PythonOperator(
    task_id="fetch_strackr_transactions",
    python_callable=fetch_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Fetch Strackr Transactions
    
    Fetches transactions from Strackr API for yesterday's date.
    
    **What it does:**
    - Gets yesterday's date range
    - Calls Strackr API with authentication
    - Handles pagination automatically
    - Returns raw transaction data
    """,
)

# Task 2: Transform transactions to normalized format
transform_task = PythonOperator(
    task_id="transform_strackr_transactions",
    python_callable=transform_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Transform Strackr Transactions
    
    Transforms raw Strackr data to normalized format using Pydantic models.
    
    **What it does:**
    - Takes raw transactions from fetch task
    - Validates and transforms each transaction
    - Filters out invalid transactions
    - Returns normalized transaction data
    """,
)

# Task 3: Upload to Supabase
upload_task = PythonOperator(
    task_id="upload_to_supabase",
    python_callable=upload_to_supabase,
    dag=dag,
    doc_md="""
    ## Upload to Supabase
    
    Uploads normalized transactions to Supabase database.
    
    **What it does:**
    - Takes normalized transactions from transform task
    - Uploads to Supabase in batches
    - Handles duplicates with upsert
    - Provides upload statistics
    """,
)

# Define task dependencies - simple linear flow
fetch_task >> transform_task >> upload_task

# Add DAG documentation
dag.doc_md = """
# Simplified Strackr Transaction DAG

This is a simplified version of the Strackr transaction processing DAG.

## Key Improvements

- **3 files instead of 11**: Reduced complexity dramatically
- **Simple linear flow**: fetch → transform → upload
- **Pydantic validation**: Clean data models with automatic validation
- **Environment-based config**: No complex configuration files
- **Inline authentication**: No separate auth modules

## Data Flow

1. **Fetch**: Get yesterday's transactions from Strackr API
2. **Transform**: Convert to normalized format with validation
3. **Upload**: Store in Supabase with duplicate handling

## Dependencies

- Strackr API credentials in environment variables or Secret Manager
- Supabase connection configured via environment variables
- Required packages: requests, pydantic, supabase-py

## Monitoring

- Email notifications on failures
- Detailed logging for each step
- Task-level error handling and recovery
"""

if __name__ == "__main__":
    dag.test()
