#!/usr/bin/env python3
"""
Script to check Mixpanel event properties and values for the new dashboard components.
This will verify the exact property values we need for implementation.
"""

import os
import sys
import json
from datetime import datetime, timedelta

# Add the dags directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags"))

from dependencies.mixpanel.mixpanel_client import MixpanelClient


def check_event_properties():
    """Check the properties and values for our target events."""

    # Get credentials from environment
    project_id = os.getenv("MIXPANEL_PROJECT_ID")
    username = os.getenv("MIXPANEL_SERVICE_USERNAME")
    secret = os.getenv("MIXPANEL_SERVICE_SECRET")
    workspace_id = os.getenv("MIXPANEL_WORKSPACE_ID")

    if not all([project_id, username, secret]):
        print("❌ Missing Mixpanel credentials in environment variables")
        return

    # Initialize client
    client = MixpanelClient(
        project_id=project_id,
        service_account_username=username,
        service_account_secret=secret,
        workspace_id=workspace_id,
        respect_rate_limits=True,
    )

    # Use recent date with data
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    print(f"🔍 Checking Mixpanel properties for date: {yesterday}")
    print("=" * 60)

    # 1. Check if our target events exist and get counts
    print("\n1️⃣ Checking event existence and counts...")
    events_to_check = ["phia_clicked", "phia_shown", "page_view", "heartbeat"]

    try:
        events_data = client.get_events_data(
            events=events_to_check,
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day",
        )

        if events_data:
            print("✅ Event counts found:")
            for event, data in events_data.get("data", {}).items():
                if data and yesterday in data:
                    count = data[yesterday]
                    print(f"   📊 {event}: {count:,} events")
                else:
                    print(f"   ⚠️ {event}: No data for {yesterday}")
        else:
            print("❌ No events data returned")

    except Exception as e:
        print(f"❌ Error checking events: {e}")

    # 2. Check platform values for phia_clicked
    print("\n2️⃣ Checking platform values for 'phia_clicked' event...")
    try:
        platform_data = client.get_segmentation_data(
            event="phia_clicked",
            segment_property="properties[platform]",
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day",
        )

        if platform_data and "data" in platform_data:
            print("✅ Platform values found:")
            for date, segments in platform_data["data"].items():
                if segments:
                    for platform, count in segments.items():
                        print(f"   🔧 {platform}: {count:,} events")
                else:
                    print(f"   ⚠️ No platform segments for {date}")
        else:
            print("❌ No platform segmentation data returned")

    except Exception as e:
        print(f"❌ Error checking platform values: {e}")

    # 3. Check platform values for phia_shown
    print("\n3️⃣ Checking platform values for 'phia_shown' event...")
    try:
        platform_data = client.get_segmentation_data(
            event="phia_shown",
            segment_property="properties[platform]",
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day",
        )

        if platform_data and "data" in platform_data:
            print("✅ Platform values found:")
            for date, segments in platform_data["data"].items():
                if segments:
                    for platform, count in segments.items():
                        print(f"   🔧 {platform}: {count:,} events")
                else:
                    print(f"   ⚠️ No platform segments for {date}")
        else:
            print("❌ No platform segmentation data returned")

    except Exception as e:
        print(f"❌ Error checking platform values: {e}")

    # 4. Check hostname values for site breakdown
    print("\n4️⃣ Checking hostname values for 'phia_clicked' event...")
    try:
        hostname_data = client.get_segmentation_data(
            event="phia_clicked",
            segment_property="properties[hostname]",
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day",
        )

        if hostname_data and "data" in hostname_data:
            print("✅ Hostname values found (top 10):")
            for date, segments in hostname_data["data"].items():
                if segments:
                    # Sort by count and show top 10
                    sorted_hosts = sorted(
                        segments.items(), key=lambda x: x[1], reverse=True
                    )[:10]
                    for hostname, count in sorted_hosts:
                        print(f"   🌐 {hostname}: {count:,} events")
                else:
                    print(f"   ⚠️ No hostname segments for {date}")
        else:
            print("❌ No hostname segmentation data returned")

    except Exception as e:
        print(f"❌ Error checking hostname values: {e}")

    # 5. Check domain values for page_view (onboarding funnel)
    print("\n5️⃣ Checking domain values for 'page_view' event...")
    try:
        domain_data = client.get_segmentation_data(
            event="page_view",
            segment_property="properties[domain]",
            from_date=yesterday,
            to_date=yesterday,
            event_type="general",
            unit="day",
        )

        if domain_data and "data" in domain_data:
            print("✅ Domain values found:")
            for date, segments in domain_data["data"].items():
                if segments:
                    # Look specifically for phia.com and show all domains
                    for domain, count in segments.items():
                        marker = "🎯" if "phia.com" in domain else "🌐"
                        print(f"   {marker} {domain}: {count:,} events")
                else:
                    print(f"   ⚠️ No domain segments for {date}")
        else:
            print("❌ No domain segmentation data returned")

    except Exception as e:
        print(f"❌ Error checking domain values: {e}")

    print("\n" + "=" * 60)
    print("✅ Property check complete!")
    print("\n📋 Summary of what we found:")
    print("   - Event names: phia_clicked, phia_shown, page_view, heartbeat")
    print("   - Properties: platform, hostname, domain, phia_id, url")
    print("   - Ready to implement the missing dashboard components!")


if __name__ == "__main__":
    check_event_properties()
