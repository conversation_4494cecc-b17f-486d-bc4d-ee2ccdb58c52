#!/bin/bash

# Discover all available events in Mixpanel project
# This script helps us find the actual event names to use in our client

# Mixpanel credentials
PROJECT_ID="3318299"
USERNAME="phia-interior-dashboard.f2b854.mp-service-account"
SECRET="wEbByBryUFALWlAG7A5fsqbJK1SGpSUG"
WORKSPACE_ID="3822915"

BASE_URL="https://mixpanel.com/api/query"

# Date range (last 30 days to get more data)
END_DATE=$(date +%Y-%m-%d)
START_DATE=$(date -v-30d +%Y-%m-%d 2>/dev/null || date -d '30 days ago' +%Y-%m-%d 2>/dev/null || echo "2025-06-01")

echo "🔍 Discovering events in Mixpanel project: $PROJECT_ID"
echo "📅 Date range: $START_DATE to $END_DATE"
echo ""

# Test specific extension-related events that might exist
echo "🎯 Testing extension-related events..."

EXTENSION_EVENTS='[
  "heartbeat", 
  "heartbeat_with_activity",
  "page_view", 
  "extension_page_view",
  "extension_loaded", 
  "extension_enabled",
  "extension_disabled",
  "extension_installed",
  "extension_uninstalled",
  "first_launch",
  "tutorial_started",
  "tutorial_completed", 
  "feature_activated",
  "feature_used",
  "onboarding_completed",
  "user_signup",
  "user_login",
  "chat_started",
  "chat_message_sent",
  "support_ticket_created"
]'

curl -s -u "$USERNAME:$SECRET" \
  -G "$BASE_URL/events" \
  --data-urlencode "project_id=$PROJECT_ID" \
  --data-urlencode "workspace_id=$WORKSPACE_ID" \
  --data-urlencode "event=$EXTENSION_EVENTS" \
  --data-urlencode "type=general" \
  --data-urlencode "unit=day" \
  --data-urlencode "from_date=$START_DATE" \
  --data-urlencode "to_date=$END_DATE" | jq '.' > extension_events.json

echo "📊 Events with data in the last 30 days:"
echo "----------------------------------------"

# Parse and display events with actual data
jq -r '.data.values | to_entries[] | select(.value | add > 0) | "\(.key): \(.value | add) total events"' extension_events.json 2>/dev/null | sort -nr -k2

echo ""
echo "📈 Daily breakdown for top events:"
echo "-----------------------------------"

# Show daily breakdown for events with data
jq -r '.data.values | to_entries[] | select(.value | add > 0) | .key' extension_events.json 2>/dev/null | head -5 | while read event; do
    echo "🔸 $event:"
    jq -r ".data.values[\"$event\"] | to_entries[] | \"  \(.key): \(.value)\"" extension_events.json 2>/dev/null
    echo ""
done

echo ""
echo "🔍 Testing unique users for active events..."

# Get unique users for the events that have data
ACTIVE_EVENTS=$(jq -r '.data.values | to_entries[] | select(.value | add > 0) | .key' extension_events.json 2>/dev/null | jq -R -s 'split("\n") | map(select(length > 0))')

if [ "$ACTIVE_EVENTS" != "[]" ] && [ "$ACTIVE_EVENTS" != "" ]; then
    curl -s -u "$USERNAME:$SECRET" \
      -G "$BASE_URL/events" \
      --data-urlencode "project_id=$PROJECT_ID" \
      --data-urlencode "workspace_id=$WORKSPACE_ID" \
      --data-urlencode "event=$ACTIVE_EVENTS" \
      --data-urlencode "type=unique" \
      --data-urlencode "unit=day" \
      --data-urlencode "from_date=$START_DATE" \
      --data-urlencode "to_date=$END_DATE" | jq '.' > unique_users_events.json

    echo "👥 Unique users for active events:"
    echo "-----------------------------------"
    jq -r '.data.values | to_entries[] | "\(.key): \(.value | add) unique users"' unique_users_events.json 2>/dev/null | sort -nr -k2
fi

echo ""
echo "🌐 Testing segmentation by common properties..."

# Test segmentation for the most active event
TOP_EVENT=$(jq -r '.data.values | to_entries[] | select(.value | add > 0) | [.key, (.value | add)] | @tsv' extension_events.json 2>/dev/null | sort -nr -k2 | head -1 | cut -f1)

if [ "$TOP_EVENT" != "" ]; then
    echo "🎯 Segmenting '$TOP_EVENT' by browser:"
    
    curl -s -u "$USERNAME:$SECRET" \
      -G "$BASE_URL/segmentation" \
      --data-urlencode "project_id=$PROJECT_ID" \
      --data-urlencode "workspace_id=$WORKSPACE_ID" \
      --data-urlencode "event=$TOP_EVENT" \
      --data-urlencode "from_date=$START_DATE" \
      --data-urlencode "to_date=$END_DATE" \
      --data-urlencode "on=properties.\$browser" \
      --data-urlencode "type=unique" \
      --data-urlencode "unit=day" \
      --data-urlencode "limit=10" | jq '.' > segmentation_browser.json

    echo "🌐 Top browsers:"
    jq -r '.data.values | keys[]' segmentation_browser.json 2>/dev/null | head -5

    echo ""
    echo "🎯 Segmenting '$TOP_EVENT' by URL/domain:"
    
    curl -s -u "$USERNAME:$SECRET" \
      -G "$BASE_URL/segmentation" \
      --data-urlencode "project_id=$PROJECT_ID" \
      --data-urlencode "workspace_id=$WORKSPACE_ID" \
      --data-urlencode "event=$TOP_EVENT" \
      --data-urlencode "from_date=$START_DATE" \
      --data-urlencode "to_date=$END_DATE" \
      --data-urlencode "on=properties.url" \
      --data-urlencode "type=unique" \
      --data-urlencode "unit=day" \
      --data-urlencode "limit=10" | jq '.' > segmentation_url.json

    echo "🔗 Top URLs/domains:"
    jq -r '.data.values | keys[]' segmentation_url.json 2>/dev/null | head -5
fi

echo ""
echo "=" * 50
echo "📋 DISCOVERY SUMMARY"
echo "=" * 50

echo "✅ Files generated:"
echo "   - extension_events.json: Event data for 30 days"
echo "   - unique_users_events.json: Unique user counts"
echo "   - segmentation_browser.json: Browser segmentation"
echo "   - segmentation_url.json: URL segmentation"

echo ""
echo "🎯 Key findings:"
echo "   - Active events: $(jq -r '.data.values | to_entries[] | select(.value | add > 0) | .key' extension_events.json 2>/dev/null | wc -l | tr -d ' ') events with data"
echo "   - Top event: $TOP_EVENT"
echo "   - Date range tested: $START_DATE to $END_DATE"

echo ""
echo "💡 Next steps:"
echo "   1. Review extension_events.json for all available events"
echo "   2. Update MixpanelClient event names based on actual data"
echo "   3. Test segmentation properties for site-based analysis"
echo "   4. Run Python client with discovered event names"
